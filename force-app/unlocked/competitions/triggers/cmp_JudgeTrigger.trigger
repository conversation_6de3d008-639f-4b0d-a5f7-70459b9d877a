trigger cmp_JudgeTrigger on cmp_Judge__c (after insert) {

    if (Trigger.isInsert && Trigger.isAfter) {
        Set<Id> contactIds = new Set<Id>();
        List<String> emails = new List<String>();
        Id competitionId = null;
        for (cmp_Judge__c record: Trigger.new) {
            competitionId = record.Competition__c;
            if (record.Contact__c != null) {
                contactIds.add(record.Contact__c);
            }
            if (record.InvitedByEmail__c != null) {
                emails.add(record.InvitedByEmail__c);
            }
        }

        // Update roles
        // Invite by email (to contacts)
        cmp_CompetitionInviteJudgeTriggerHandler.updateContactByJudgeRole(contactIds);
        cmp_CompetitionInviteJudgeTriggerHandler.sendInviteToJudges(competitionId, contactIds);


        // Invite by email (to emails)
        cmp_CompetitionInviteJudgeTriggerHandler.sendInviteToJudges(competitionId, emails);
    }

}
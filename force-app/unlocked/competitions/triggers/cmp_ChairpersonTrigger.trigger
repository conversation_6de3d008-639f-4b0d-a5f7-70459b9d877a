trigger cmp_ChairpersonTrigger on cmp_Chairperson__c (after insert) {

    if (Trigger.isInsert && Trigger.isAfter) {
        Set<Id> contactIds = new Set<Id>();
        for (cmp_Chairperson__c record: Trigger.new) {
            contactIds.add(record.Chairperson__c);
        }
        List<Contact> contacts = [
                SELECT Id, PortalRole__c FROM Contact WHERE Id IN :contactIds AND PortalRole__c EXCLUDES ('Technical Chair')
        ];

        for (Contact theContact: contacts) {
            theContact.PortalRole__c = theContact.PortalRole__c + ';Technical Chair';
        }
        update contacts;
    }

}
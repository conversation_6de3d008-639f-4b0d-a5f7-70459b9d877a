trigger cmp_ParticipantTrigger on cmp_Participant__c (before insert, after insert, after delete) {

    if (Trigger.isBefore && Trigger.isInsert) {
        cmp_ParticipantTriggerHandler.updateIndividualNumber(Trigger.new, true);
    }
    if (Trigger.isAfter && Trigger.isInsert) {
        cmp_ParticipantTriggerHandler.assignAssessments(Trigger.new);
    }
    else if (Trigger.isAfter && Trigger.isDelete) {
        cmp_ParticipantTriggerHandler.deleteAssignments(Trigger.old);
    }

}
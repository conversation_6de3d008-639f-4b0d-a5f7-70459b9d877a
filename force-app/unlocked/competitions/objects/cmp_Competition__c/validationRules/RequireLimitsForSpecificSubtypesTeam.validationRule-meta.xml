<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>RequireLimitsForSpecificSubtypesTeam</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
  $Setup.cmp_Settings__c.EnableLimitsForCompetitions__c,
  OR(
    UPPER( TEXT( Subtype__c ) ) = &apos;SIMPLE&apos;,
    UPPER( TEXT( Subtype__c ) ) = &apos;DISTRICT QUALIFIERS&apos;,
    UPPER( TEXT( Subtype__c ) ) = &apos;BY INVITATION ONLY&apos;
  ),
  IsTeamCompetition__c,
  OR(
     ISBLANK( LimitOfCompetitors__c ),
     ISBLANK( LimitOfTeams__c )
  )
)</errorConditionFormula>
    <errorMessage>The Limit Of Teams and Limit Of Competitors fields are required if the subtype of competition is marked as &apos;SIMPLE&apos; or &apos;DISTRICT QUALIFIERS&apos; or &apos;BY INVITATION ONLY&apos; and this is the Team competition.</errorMessage>
</ValidationRule>

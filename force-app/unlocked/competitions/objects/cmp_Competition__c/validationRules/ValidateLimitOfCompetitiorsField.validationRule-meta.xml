<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ValidateLimitOfCompetitiorsField</fullName>
    <active>true</active>
    <errorConditionFormula>OR (
        AND(ISPICKVAL(TeamRoles__c, &apos;A B&apos;), LimitOfCompetitors__c != 2),
        AND(ISPICKVAL(TeamRoles__c, &apos;A B D&apos;), LimitOfCompetitors__c != 3),
        AND(ISPICKVAL(TeamRoles__c, &apos;A B C&apos;), LimitOfCompetitors__c != 3),
        AND(ISPICKVAL(TeamRoles__c, &apos;A B C D&apos;), LimitOfCompetitors__c != 4),
        AND(ISPICKVAL(TeamRoles__c, &apos;A D C D&apos;), LimitOfCompetitors__c != 4),
        AND(ISPICKVAL(TeamRoles__c, &apos;A B C D E F&apos;), LimitOfCompetitors__c != 6),
        AND(ISPICKVAL(TeamRoles__c, &apos;A B C D E F G&apos;), LimitOfCompetitors__c != 7),
        AND(ISPICKVAL(TeamRoles__c, &apos;C E M P&apos;), LimitOfCompetitors__c != 4),
        AND(ISPICKVAL(TeamRoles__c, &apos;C M&apos;), LimitOfCompetitors__c != 2)
        )</errorConditionFormula>
    <errorMessage>The Team Limit field must the same as number of roles inside the Team Roles field</errorMessage>
</ValidationRule>

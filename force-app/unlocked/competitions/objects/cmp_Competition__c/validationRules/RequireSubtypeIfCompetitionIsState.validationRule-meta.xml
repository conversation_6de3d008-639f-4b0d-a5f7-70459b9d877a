<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>RequireSubtypeIfCompetitionIsState</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
  TEXT( Type__c ) = &apos;State&apos;, 
  ISBLANK( TEXT( Subtype__c ) )
)</errorConditionFormula>
    <errorDisplayField>Subtype__c</errorDisplayField>
    <errorMessage>The Subtype field is required if the Competition is marked as a State competition.</errorMessage>
</ValidationRule>

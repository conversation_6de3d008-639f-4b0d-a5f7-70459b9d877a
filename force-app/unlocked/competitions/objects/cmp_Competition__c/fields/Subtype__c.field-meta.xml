<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Subtype__c</fullName>
    <externalId>false</externalId>
    <inlineHelpText>The field is required if the Type equals the State value.</inlineHelpText>
    <label>Subtype</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>SIMPLE</fullName>
                <default>false</default>
                <label>SIMPLE</label>
            </value>
            <value>
                <fullName>DISTRICT QUALIFIERS</fullName>
                <default>false</default>
                <label>DISTRICT QUALIFIERS</label>
            </value>
            <value>
                <fullName>FRESHMAN ONLY</fullName>
                <default>false</default>
                <label>FRESHMAN ONLY</label>
            </value>
            <value>
                <fullName>BY INVITATION ONLY</fullName>
                <default>false</default>
                <label>BY INVITATION ONLY</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>

<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ButtonStyle__c</fullName>
    <description>Will be used if Type is selected as Button</description>
    <externalId>false</externalId>
    <label>Button Style</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>neutral</fullName>
                <default>false</default>
                <label>neutral</label>
            </value>
            <value>
                <fullName>brand</fullName>
                <default>false</default>
                <label>brand</label>
            </value>
            <value>
                <fullName>outline-brand</fullName>
                <default>false</default>
                <label>outline-brand</label>
            </value>
            <value>
                <fullName>destructive</fullName>
                <default>false</default>
                <label>destructive</label>
            </value>
            <value>
                <fullName>text-destructive</fullName>
                <default>false</default>
                <label>text-destructive</label>
            </value>
            <value>
                <fullName>success</fullName>
                <default>false</default>
                <label>success</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>

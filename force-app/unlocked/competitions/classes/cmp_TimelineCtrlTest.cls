@IsTest
private class cmp_TimelineCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact judge = cm_TestUtils.createContact(theAccount.Id, 'Judge');
            PortalUser.create(judge);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cmp_Competition__c competition = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School'
            );
            insert competition;

            cmp_Chairperson__c chairperson = new cmp_Chairperson__c(
                    Chairperson__c = leadAdvisor.Id,
                    Competition__c = competition.Id
            );
            insert chairperson;

            cmp_Judge__c assignedJudge = new cmp_Judge__c(
                    Competition__c = competition.Id,
                    Contact__c = judge.Id
            );
            insert  assignedJudge;

            insert new cmp_Timeline__c(Competition__c = competition.Id, Deadline__c = System.today());
        }
    }

    @IsTest
    static void testGetCompetitionDetails() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            cmp_TimelineCtrl.getCompetitionDetails(CMP.Id);
        }
    }

    @IsTest
    static void testGetCompetitionDetailsFail() {
        Boolean hasError = false;
        System.runAs(cm_TestUtils.getAdmin()) {
            try {
                cmp_Competition__c CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
                cmp_TimelineCtrl.getCompetitionDetails(CMP.Id);
            } catch (Exception e) {
                hasError = true;
            }
        }

        Assert.isTrue(hasError);
    }

    @IsTest
    static void testGetTimelinesByCompetitionId() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            cmp_TimelineCtrl.getTimelinesByCompetitionId(CMP.Id);
        }
    }

    @IsTest
    static void testSavePrizeInfo() {
        System.runAs(cm_TestUtils.getAdmin()) {
            cmp_Competition__c CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
            cmp_Timeline__c timeline = [SELECT Id FROM cmp_Timeline__c LIMIT 1];
            cmp_TimelineCtrl.savePrizeInfo(timeline.Id, '', '', '', '');
        }
    }
}
@IsTest
private class cmp_OverviewCtrlTest {

    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact judge = cm_TestUtils.createContact(theAccount.Id, 'Judge');
            PortalUser.create(judge);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cmp_Competition__c competition = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School'
            );
            insert competition;

            cmp_Judge__c assignedJudge = new cmp_Judge__c(
                    Competition__c = competition.Id,
                    Contact__c = judge.Id
            );
            insert  assignedJudge;
        }
    }

    @IsTest
    static void testBehavior() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            cmp_OverviewCtrl.getCompetitionDetails(CMP.Id);
        }
    }

    @IsTest
    static void testBehaviorFail() {
        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            try {
                cmp_OverviewCtrl.getCompetitionDetails('00x000000000000');
            } catch (Exception e) {
            }
        }
    }
}
@IsTest
public class cmp_CompetitionsSelectionCtrlTest {
    @TestSetup
    static void setup() {

        User admin = cm_TestUtils.createAdminUser();

        PermissionSet PS = [SELECT Id FROM PermissionSet WHERE Name = 'cmp_AdminPermissions'];
        insert new PermissionSetAssignment(AssigneeId = admin.Id, PermissionSetId = PS.Id);

        System.runAs(cm_TestUtils.getAdmin()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            leadAdvisor.Division__c = 'Middle School';
            PortalUser.create(leadAdvisor);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cm_RegistrationType__c registrationType = new cm_RegistrationType__c(
                    Name = 'Student',
                    IsActive__c = true,
                    Conference__c = conference.Id,
                    IsCompetitionsSelectionAvailable__c = true
            );
            insert registrationType;

            cm_Participant__c cmParticipant = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = leadAdvisor.Id,
                    RegistrationStatus__c = 'Approved'
            );
            insert cmParticipant;

            cmp_Competition__c competition1 = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School',
                    IsOpenForRegistration__c = true
            );
            cmp_Competition__c competition2 = new cmp_Competition__c(
                    Name = 'Second',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School',
                    IsOpenForRegistration__c = true
            );
            cmp_Competition__c competition3 = new cmp_Competition__c(
                    Name = 'Third',
                    Conference__c = conference.Id,
                    Division__c = 'High School',
                    IsOpenForRegistration__c = true
            );

            insert new List<cmp_Competition__c>{ competition1, competition2, competition3 };

            insert new cmp_Participant__c(
                    ConferenceParticipant__c = cmParticipant.Id,
                    Competition__c = competition1.Id,
                    Participant__c = leadAdvisor.Id
            );
        }

    }

    @IsTest
    static void getCompetitionsSuccess() {
        cmp_CompetitionsSelectionCtrl.Response competitions;
        cm_Conference__c conference;

        System.runAs(cm_TestUtils.getAdmin()) {
            conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
        }

//        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
        System.runAs(cm_TestUtils.getAdmin()) {
            Contact theContact = [SELECT Id, Division__c FROM Contact LIMIT 1];
            competitions = cmp_CompetitionsSelectionCtrl.getCompetitions(
                conference.Id,
                theContact.Id
            );
        }

//        Assert.areEqual(2, competitions.allCompetitions.size(), 'Two Conference Competitions must be created');
    }

    @IsTest
    static void getCompetitionsError() {
        Boolean isError = false;
        Id wrongId = '000000000000000000';

        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    cmp_CompetitionsSelectionCtrl.getCompetitions(wrongId, wrongId);
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(true, isError, 'Error must exist');
    }

    @IsTest
    static void createUpdateParticipantCompetitions() {

        System.runAs(cm_TestUtils.getAdmin()) {
            Contact theContact = [SELECT Id FROM Contact LIMIT 1];
            cmp_Competition__c competition = [SELECT Id FROM cmp_Competition__c WHERE Name = 'Second' LIMIT 1];

            List<cmp_CompetitionsSelectionCtrl.CompetitionOrder> orderedCompetitions = new List<cmp_CompetitionsSelectionCtrl.CompetitionOrder>();
            cmp_CompetitionsSelectionCtrl.CompetitionOrder competitionOrder = new cmp_CompetitionsSelectionCtrl.CompetitionOrder();
            competitionOrder.competitionId = competition.Id;
            competitionOrder.order = 1;
            orderedCompetitions.add(competitionOrder);

            cmp_CompetitionsSelectionCtrl.updateParticipantCompetitions(
                    theContact.Id,
                    orderedCompetitions
            );
        }

//        Assert.areEqual(2, participants.size(), 'Contact must be register to one competition');
    }

}
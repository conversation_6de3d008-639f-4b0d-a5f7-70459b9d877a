global with sharing class cmp_AssessmentPasscodeScheduler implements Schedulable {

    global void execute(SchedulableContext ctx) {
        // STEP 1: Retrieve the Assessment -> Passcode map (Assessments that start tomorrow)
//        Map<Id, String> assessmentIdToPassCodeMap = getAssessmentIdToPassCodeMap();
//
//        // STEP 2: From that map, get all the Assessment IDs
//        Set<Id> assessmentIds = extractAssessmentIds(assessmentIdToPassCodeMap);
//
//        // STEP 3: Query competitions
//        List<cmp_Competition__c> competitionList = getCompetitions(assessmentIds);
//
//        // STEP 4: Build the AccountId -> (AssessmentId -> PassCode) map
//        Map<Id, Map<Id, String>> accountIdToAssessmentMap = buildAccountIdToAssessmentMap(competitionList);
//
//        // STEP 5: Collect all unique AssessmentIds from the account->assessment map
//        Set<Id> allAssessmentIdsInCompetitions = extractAssessmentIdsFromAccountMap(accountIdToAssessmentMap);
//
//        // STEP 6: Query Assessment__c records to build a map of (AssessmentId -> AssessmentName)
//        Map<Id, String> assessmentIdToNameMap = getAssessmentIdToNameMap(allAssessmentIdsInCompetitions);
//
//        // STEP 7: Get a list of Lead Advisors in these accounts
//        List<Contact> leadAdvisors = getLeadAdvisors(accountIdToAssessmentMap.keySet());
//
//        // STEP 8: Send emails to Lead Advisors
//        sendEmailsToAdvisors(leadAdvisors, accountIdToAssessmentMap, assessmentIdToNameMap);
    }

    /**
     * STEP 1 LOGIC: Retrieves a Map where the key is Assessment__c (Id)
     * and the value is Assessment__r.PassCode__c (String).
     * Uses a single SOQL query with a child subquery for tomorrow.
     */
//    private static Map<Id, String> getAssessmentIdToPassCodeMap() {
//        List<as_DistrictOpenDates__c> districtOpenDatesList = [
//                SELECT Id,
//                (SELECT Id, Assessment__c, Assessment__r.PassCode__c
//                FROM AssessmentDistrictOpenDates__r)
//                FROM as_DistrictOpenDates__c
//                WHERE StartDate__c = TOMORROW
//        ];
//
//        Map<Id, String> assessmentIdToPassCodeMap = new Map<Id, String>();
//        for (as_DistrictOpenDates__c districtOpenDate : districtOpenDatesList) {
//            for (as_AssessmentDistrictOpenDates__c childADOD : districtOpenDate.AssessmentDistrictOpenDates__r) {
//                if (childADOD.Assessment__r?.PassCode__c != null) {
//                    assessmentIdToPassCodeMap.put(childADOD.Assessment__c, childADOD.Assessment__r.PassCode__c);
//                }
//            }
//        }
//        return assessmentIdToPassCodeMap;
//    }
//
//    /**
//     * STEP 2 LOGIC: Extract Assessment IDs from the map.
//     */
//    private static Set<Id> extractAssessmentIds(Map<Id, String> assessmentIdToPassCodeMap) {
//        return new Set<Id>(assessmentIdToPassCodeMap.keySet());
//    }
//
//    /**
//     * STEP 3 LOGIC: Returns a list of cmp_Competition__c records for given Assessments & Conferences.
//     */
//    private static List<cmp_Competition__c> getCompetitions(Set<Id> assessmentIds) {
//        return [
//                SELECT Id,
//                        Name,
//                        AssessmentEmployability__c,
//                        AssessmentEmployability__r.PassCode__c,
//                        AssessmentSafety__c,
//                        AssessmentSafety__r.PassCode__c,
//                        AssessmentTech__c,
//                        AssessmentTech__r.PassCode__c,
//                (
//                        SELECT Participant__r.AccountId
//                        FROM CompetitionParticipants__r
//                )
//                FROM cmp_Competition__c
//                WHERE (
//                        AssessmentEmployability__c IN :assessmentIds
//                        OR AssessmentSafety__c      IN :assessmentIds
//                        OR AssessmentTech__c        IN :assessmentIds
//                )
//        ];
//    }
//
//    /**
//     * STEP 4 LOGIC:
//     * Given a list of cmp_Competition__c, builds a nested map:
//     *   AccountId -> (AssessmentId -> PassCode).
//     */
//    private static Map<Id, Map<Id, String>> buildAccountIdToAssessmentMap(
//            List<cmp_Competition__c> competitionList
//    ) {
//        Map<Id, Map<Id, String>> accountIdToAssessmentMap = new Map<Id, Map<Id, String>>();
//
//        for (cmp_Competition__c competition : competitionList) {
//            // A mini map of AssessmentId -> PassCode for this competition
//            Map<Id, String> compAssessmentMap = new Map<Id, String>();
//
//            if (competition.AssessmentEmployability__c != null) {
//                compAssessmentMap.put(
//                        competition.AssessmentEmployability__c,
//                        competition.AssessmentEmployability__r.PassCode__c
//                );
//            }
//            if (competition.AssessmentSafety__c != null) {
//                compAssessmentMap.put(
//                        competition.AssessmentSafety__c,
//                        competition.AssessmentSafety__r.PassCode__c
//                );
//            }
//            if (competition.AssessmentTech__c != null) {
//                compAssessmentMap.put(
//                        competition.AssessmentTech__c,
//                        competition.AssessmentTech__r.PassCode__c
//                );
//            }
//
//            // Loop the participant subquery (LIMIT 1 returns at most one row)
//            for (cmp_Participant__c participantRecord : competition.CompetitionParticipants__r) {
//                Id accountId = participantRecord.Participant__r.AccountId;
//
//                if (!accountIdToAssessmentMap.containsKey(accountId)) {
//                    accountIdToAssessmentMap.put(accountId, new Map<Id, String>());
//                }
//                Map<Id, String> assessmentMapForAccount = accountIdToAssessmentMap.get(accountId);
//
//                // Merge each assessment/passcode into that account’s sub-map
//                for (Id assessmentId : compAssessmentMap.keySet()) {
//                    assessmentMapForAccount.put(assessmentId, compAssessmentMap.get(assessmentId));
//                }
//            }
//        }
//
//        return accountIdToAssessmentMap;
//    }
//
//    /**
//     * STEP 5 LOGIC:
//     * From a nested map (AccountId -> (AssessmentId -> PassCode)),
//     * gather every unique AssessmentId into a single Set.
//     */
//    private static Set<Id> extractAssessmentIdsFromAccountMap(Map<Id, Map<Id, String>> accountIdToAssessmentMap) {
//        Set<Id> allAssessmentIds = new Set<Id>();
//        for (Map<Id, String> subMap : accountIdToAssessmentMap.values()) {
//            allAssessmentIds.addAll(subMap.keySet());
//        }
//        return allAssessmentIds;
//    }
//
//    /**
//     * STEP 6 LOGIC:
//     * Queries Assessment__c records to get their Names by the given set of IDs.
//     */
//    private static Map<Id, String> getAssessmentIdToNameMap(Set<Id> assessmentIds) {
//        Map<Id, String> results = new Map<Id, String>();
//        if (assessmentIds.isEmpty()) {
//            return results;  // Return empty map if no IDs
//        }
//
//        for (as_Assessment__c assessment : [
//                SELECT Id, Name
//                FROM as_Assessment__c
//                WHERE Id IN :assessmentIds
//        ]) {
//            results.put(assessment.Id, assessment.Name);
//        }
//        return results;
//    }
//
//    /**
//     * STEP 7 LOGIC:
//     * Get a list of Contacts whose PortalRole__c includes 'Lead Advisor'
//     * and whose AccountId is in the provided set.
//     */
//    private static List<Contact> getLeadAdvisors(Set<Id> accountIds) {
//        if (accountIds.isEmpty()) {
//            return new List<Contact>();
//        }
//        return [
//                SELECT Id, Name, Email, AccountId, PortalRole__c
//                FROM Contact
//                WHERE AccountId IN :accountIds
//                AND PortalRole__c INCLUDES ('Lead Advisor')
//        ];
//    }
//
//    /**
//     * STEP 8 LOGIC:
//     * Sends emails to every Lead Advisor, listing tomorrow's assessments
//     * (by Name) and their passcodes.
//     */
//    private static void sendEmailsToAdvisors(
//            List<Contact> leadAdvisors,
//            Map<Id, Map<Id, String>> accountIdToAssessmentMap,
//            Map<Id, String> assessmentIdToNameMap
//    ) {
//        if (leadAdvisors.isEmpty()) {
//            return;
//        }
//        String orgWideId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
//        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
//
//        for (Contact advisor : leadAdvisors) {
//            // Retrieve this Account’s sub-map (AssessmentId -> PassCode)
//            Map<Id, String> subMap = accountIdToAssessmentMap.get(advisor.AccountId);
//
//            if (subMap != null && !subMap.isEmpty()) {
//                // Build the email body
//                String body = 'Hello ' + advisor.Name + ',\n\n';
//                body += 'Tomorrow\'s assessments and their passcodes are listed below. '
//                        +  'Please share them with participants if needed:\n\n';
//
//                for (Id assessmentId : subMap.keySet()) {
//                    String passCode = subMap.get(assessmentId);
//                    String assessmentName = assessmentIdToNameMap.get(assessmentId);
//
//                    body += '• Assessment: ' + assessmentName
//                            +  ' | Passcode: ' + passCode
//                            +  '\n';
//                }
//
//                // Create the email message
//                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
//                email.setToAddresses(new String[] { advisor.Email });
//                email.setOrgWideEmailAddressId(orgWideId);
//                email.setSubject('Tomorrow\'s Assessment Passcodes');
//                email.setPlainTextBody(body);
//
//                emails.add(email);
//            }
//        }
//
//        if (!emails.isEmpty()) {
//            Messaging.sendEmail(emails);
//        }
//    }
}

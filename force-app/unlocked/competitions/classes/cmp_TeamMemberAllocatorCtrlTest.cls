@IsTest
private class cmp_TeamMemberAllocatorCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');

            PortalUser.create(leadAdvisor);

            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cm_Participant__c cmParticipant = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = leadAdvisor.Id,
                    RegistrationStatus__c = 'Approved'
            );
            insert cmParticipant;

            cmp_Competition__c competition = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School'
            );
            insert competition;

            cmp_Participant__c participant = new cmp_Participant__c(
                    Competition__c = competition.Id,
                    Participant__c = student.Id,
                    ConferenceParticipant__c = cmParticipant.Id
            );
            insert participant;

        }
    }

    @IsTest
    static void testInitFail() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            try {
                cmp_TeamMemberAllocatorCtrl.init(CMP.Id);
            } catch (Exception e) {
            }
        }
    }

    @IsTest
    static void testInit() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
            CMP.IsTeamCompetition__c = true;
            update CMP;
        }

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            try {
                cmp_TeamMemberAllocatorCtrl.init(CMP.Id);
            } catch (Exception e) {

            }

        }
    }

    @IsTest
    static void testSaveFail() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
            CMP.IsTeamCompetition__c = true;
            update CMP;
        }

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            try {
                cmp_TeamMemberAllocatorCtrl.save(CMP.Id, '{}');
            } catch (Exception e) {
            }
        }
    }

    @IsTest
    static void testSave() {
        cmp_Competition__c CMP;
        Contact student;
        cmp_Participant__c participant;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
            CMP.IsTeamCompetition__c = true;
            update CMP;

            student = [SELECT Id FROM Contact WHERE PortalRole__c = 'Student'];
            participant = [SELECT Id FROM cmp_Participant__c WHERE Participant__c = :student.Id];
        }

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            cmp_TeamMemberAllocatorCtrl.save(CMP.Id, '[{"id":"' + participant.Id + '", "name": "A", "team": null, "role": "A"}]');
        }
    }
}
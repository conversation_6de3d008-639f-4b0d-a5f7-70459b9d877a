@IsTest
private class cmp_TechnicalCommitteeMembersCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');

            PortalUser.create(leadAdvisor);

            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            Contact judge = cm_TestUtils.createContact(theAccount.Id, 'Judge');
            PortalUser.create(judge);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cm_Participant__c cmParticipant = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = student.Id,
                    RegistrationStatus__c = 'Approved'
            );
            insert cmParticipant;

            cmp_Competition__c competition = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School'
            );
            insert competition;

            cmp_Participant__c participant = new cmp_Participant__c(
                    Competition__c = competition.Id,
                    Participant__c = student.Id,
                    ConferenceParticipant__c = cmParticipant.Id
            );
            insert participant;

            cmp_Judge__c assignedJudge = new cmp_Judge__c(
                    Competition__c = competition.Id,
                    Contact__c = judge.Id,
                    ApprovalStatus__c = 'Approved'
            );
            insert  assignedJudge;
        }
    }

    @IsTest
    static void testFetCompetitionDetailsFail() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            try {
                cmp_TechnicalCommitteeMembersCtrl.getCompetitionDetails(CMP.Id);
            } catch (Exception e) {
            }
        }
    }

    @IsTest
    static void testGetBookHotelURL() {
        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            cmp_TechnicalCommitteeMembersCtrl.getBookHotelURL();
        }
    }

    @IsTest
    static void testGetBookHotelURLFail() {
        System.runAs(cm_TestUtils.getAdmin()) {
            try {
                cmp_TechnicalCommitteeMembersCtrl.getBookHotelURL();
            } catch (Exception e) {

            }
        }
    }

    @IsTest
    static void testCreateJudgeInvite() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
            Contact judge = [SELECT Id FROM Contact WHERE PortalRole__c = 'Judge'];
            cmp_TechnicalCommitteeMembersCtrl.createJudgeInvite(CMP.Id, judge.Id);
        }
    }

    @IsTest
    static void testCreateJudgeInviteFail() {
        System.runAs(cm_TestUtils.getAdmin()) {
            try {
                cmp_TechnicalCommitteeMembersCtrl.createJudgeInvite(null, null);
            } catch (Exception e) {

            }
        }
    }

    @IsTest
    static void testSendEmail() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            cmp_TechnicalCommitteeMembersCtrl.sendEmail(CMP.Id, 'a', 'b');
        }
    }

    @IsTest
    static void testIsApprovedJudge() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            cmp_TechnicalCommitteeMembersCtrl.isApprovedJudge(CMP.Id);
        }
    }

    @IsTest
    static void testCreateJudgeEmailInvite() {
        cmp_Competition__c CMP;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
            Contact judge = [SELECT Id, Email FROM Contact WHERE PortalRole__c = 'Judge'];
            cmp_TechnicalCommitteeMembersCtrl.createJudgeEmailInvite(CMP.Id, judge.Email);
        }
    }
}
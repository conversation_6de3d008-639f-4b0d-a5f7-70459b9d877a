@IsTest
public class cmp_CompetitionDetailsCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs( cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact judge = cm_TestUtils.createContact(theAccount.Id, 'Judge');
            PortalUser.create(judge);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cmp_Competition__c competition = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School'
            );
            insert competition;

            cmp_Chairperson__c chairperson = new cmp_Chairperson__c(
                    Chairperson__c = leadAdvisor.Id,
                    Competition__c = competition.Id
            );
            insert chairperson;
        }
    }

//    @TestSetup
//    static void setup() {
//        User admin = cm_TestUtils.createAdmin();
//        System.runAs(admin) {
//            Conference__c conference = new Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
//            insert conference;
//
//            cmp_Competition__c competition = new cmp_Competition__c(
//                Name = 'First',
//                Conference__c = conference.Id,
//                Division__c = 'A'
//            );
//
//            insert competition;
//
//            Account testAccount = new Account(Name = 'Last Household');
//            insert testAccount;
//
//            Contact theContact = new Contact(
//                FirstName = 'Bob',
//                LastName = 'Smith',
//                Division__c = 'A',
//                AccountId = testAccount.Id
//            );
//            insert theContact;
//
//            ConferenceCompetitionChairperson__c chairperson = new ConferenceCompetitionChairperson__c(
//                Chairperson__c = theContact.Id,
//                ConferenceCompetition__c = competition.Id
//            );
//            insert chairperson;
//        }
//    }

    @IsTest
    static void getCompetitionDetailsError() {
        cmp_Competition__c competition;
        cmp_Competition__c conferenceCompetition = null;
        Boolean hasError = false;

        System.runAs(cm_TestUtils.getAdmin()) {
            conferenceCompetition = [SELECT Id FROM cmp_Competition__c LIMIT 1];
        }

        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            Test.startTest();
            {
                try {
                    competition = cmp_CompetitionDetailsCtrl.getCompetitionDetails(conferenceCompetition.Id);
                } catch (Exception e) {
                    hasError = true;
                }

            }
            Test.stopTest();
        }

        Assert.areEqual(true, hasError, 'The Conference Competitions must have an error');
    }

    @IsTest
    static void testIsTechnicalChair() {
        cmp_Competition__c CMP;
        Boolean isApprovedJudge = true;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            Test.startTest();
            {
                isApprovedJudge = cmp_CompetitionDetailsCtrl.isTechnicalChair(CMP.Id);
            }
            Test.stopTest();
        }

        Assert.areEqual(false, isApprovedJudge);

    }

    @IsTest
    static void testIsJudge() {
        cmp_Competition__c CMP;
        Boolean isApprovedJudge = true;

        System.runAs(cm_TestUtils.getAdmin()) {
            CMP = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First'];
        }

        System.runAs(cm_TestUtils.getUserByRole('Judge')) {
            Test.startTest();
            {
                isApprovedJudge = cmp_CompetitionDetailsCtrl.isApprovedJudge(CMP.Id);
            }
            Test.stopTest();
        }

        Assert.areEqual(false, isApprovedJudge);

    }

    @IsTest
    static void isJudgeFail() {
        Boolean hasError = false;

        System.runAs(cm_TestUtils.getAdmin()) {
            try {
                cmp_CompetitionDetailsCtrl.isApprovedJudge('00x000000000000');
            } catch (Exception e) {
                hasError = true;
            }
        }

        Assert.isTrue(hasError);
    }

    @IsTest
    static void testIsTechnicalChairFail() {
        Boolean hasError = false;

        System.runAs(cm_TestUtils.getAdmin()) {
            try {
                cmp_CompetitionDetailsCtrl.isTechnicalChair('00x000000000000');
            } catch (Exception e) {
                hasError = true;
            }
        }

        Assert.isTrue(hasError);
    }
}
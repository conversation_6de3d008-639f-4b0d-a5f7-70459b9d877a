public with sharing class cmp_TimelineCtrl {

    @AuraEnabled(Cacheable=true)
    public static cmp_Service.CompetitionDTO getCompetitionDetails(Id recordId) {
        try {
            return cmp_Service.getCompetitionDetails(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=true)
    public static List<cmp_Timeline__c> getTimelinesByCompetitionId(Id recordId) {
        try {
            return cmp_Selector.getTimelinesByCompetitionId(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Boolean savePrizeInfo(
            Id timelineId,
            String status,
            String goldPrize,
            String silverPrize,
            String bronzePrize
    ) {
        try {
            WS.updateRecord(new cmp_Timeline__c(
                    Id = timelineId,
                    PrizeInfoStatus__c = status,
                    PrizesForGold__c = goldPrize,
                    PrizesForSilver__c = silverPrize,
                    PrizesForBronze__c = bronzePrize
            ));
            return true;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}
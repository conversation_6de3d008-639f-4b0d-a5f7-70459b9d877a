public with sharing class cmp_Selector {

    public static cmp_Competition__c getCompetition(Id recordId) {
        return new WS().getCompetition(recordId);
    }

    public static List<cmp_Participant__c> getParticipantCompetitions(Id contactId, Id conferenceId) {
        return getParticipantCompetitions(new Set<Id> { contactId }, conferenceId);
    }

    public static List<cmp_Participant__c> getParticipantCompetitions(
        Set<Id> contactIds,
        Id conferenceId
    ) {
        return (new cmp_Selector.WS()).getParticipantCompetitions(contactIds, conferenceId);
    }

    public static List<cmp_Participant__c> getParticipantCompetitions(
        Id contactId,
        List<Id> competitionsIds
    ) {
        return [
            SELECT Id, Competition__c, PreferenceOrder__c
            FROM cmp_Participant__c
            WHERE Participant__c = :contactId AND Competition__c IN :competitionsIds
            WITH USER_MODE
        ];
    }

    public static List<cmp_Competition__c> getCompetitions(Id conferenceId, String division) {
        Id currentUserAccountId = PortalUser.getAccountId();
        return [
            SELECT
                    Id,
                    Name,
                    IsTeamCompetition__c,
                    LimitOfCompetitorsDirect__c,
                    LimitOfCompetitorsTPP__c,
                    LimitOfTeamsDirect__c,
                    LimitOfTeamsTPP__c,
                    LimitOfCompetitors__c,
                    LimitOfTeams__c,
                    Type__c,
                    Subtype__c,
                    AllowDelegate__c,
            (
                    SELECT Id, PreferenceOrder__c, ConferenceParticipant__r.RegistrationStatus__c
                    FROM CompetitionParticipants__r
                    WHERE Participant__r.AccountId = :currentUserAccountId
                    ORDER BY PreferenceOrder__c DESC
            )
            FROM cmp_Competition__c
            WHERE Conference__c = :conferenceId
            AND Division__c = :division
            AND IsOpenForRegistration__c = TRUE
            WITH USER_MODE
            ORDER BY Name
        ];
    }


    public static EmailTemplate getEmailTemplate(String developerName) {
        return [SELECT Id, Body, HtmlValue, Subject FROM EmailTemplate WHERE DeveloperName = :developerName] ?? null;
    }

    public static List<cmp_Timeline__c> getTimelinesByCompetitionId(Id recordId) {
        return [
            SELECT
                Id,
                Name,
                Deadline__c,
                Description__c,
                IconName__c,
                PrizeInfoStatus__c,
                PrizesForGold__c,
                PrizesForSilver__c,
                PrizesForBronze__c,
                (
                    SELECT Type__c, Text__c, ButtonStyle__c, ButtonLink__c, CompetitionTimeline__c
                    FROM CompetitionTimelineActions__r
                    ORDER BY Order__c
                ),
                (SELECT Id, ContentDocumentId FROM AttachedContentDocuments ORDER BY CreatedDate DESC LIMIT 1)
            FROM cmp_Timeline__c
            WHERE Competition__c = :recordId
            ORDER BY Deadline__c DESC
        ];
    }

    private without sharing class WS {
        public List<cmp_Participant__c> getParticipantCompetitions(
                Set<Id> contactIds,
                Id conferenceId
        ) {
            if (contactIds.isEmpty() || conferenceId == null) {
                return new List<cmp_Participant__c>();
            }

            return [
                SELECT Id, Competition__c, Competition__r.Name, Participant__c, PreferenceOrder__c
                FROM cmp_Participant__c
                WHERE Participant__c IN :contactIds AND Competition__r.Conference__c = :conferenceId
            ];
        }

        public cmp_Competition__c getCompetition(Id recordId) {
            return [
                    SELECT
                            Id,
                            ContactPerson1__r.Name,
                            ContactPerson1__r.Email,
                            ContactPerson2__r.Name,
                            ContactPerson2__r.Email,
                            CompetitionCluster__c,
                            CompetitionDescription__c,
                            Conference__r.DisplayCompetitorInfoForTech__c,
                            TechnicalStandardsDescription__c,
                            TechnicalStandardsDescriptionLink__c,
                            CompetitionLocationAddress__c,
                            CompetitionLocation__Latitude__s,
                            CompetitionLocation__Longitude__s,
                            CompetitionTime__c,
                            IsTeamCompetition__c,
                            BriefingLocationAddress__c,
                            BriefingLocation__Latitude__s,
                            BriefingLocation__Longitude__s,
                            BriefingTime__c,
                            BuildingPlanLink__c,
                            (
                                    SELECT Chairperson__r.Name,
                                            Chairperson__r.Email,
                                            Chairperson__r.Phone,
                                            Chairperson__r.Account.Name
                                    FROM CompetitionChairpersons__r
                            ),
                            (
                                    SELECT Contact__r.Name,
                                            Contact__r.Email,
                                            Contact__r.Account.Name,
                                            InvitedByEmail__c,
                                            ApprovalStatus__c
                                    FROM CompetitionJudges__r
                                    WHERE ApprovalStatus__c IN ('Pending', 'Approved')
                            ),
                            (
                                    SELECT
                                            Id,
                                            TeamId__c,
                                            Participant__r.FirstName,
                                            Participant__r.LastName,
                                            Participant__r.Division__c,
                                            Participant__r.Account.Name,
                                            ContestantNumberFormula__c,
                                            IsDisqualified__c
                                    FROM CompetitionParticipants__r
                            )
                    FROM cmp_Competition__c
                    WHERE Id = :recordId
            ];
        }

    }

}
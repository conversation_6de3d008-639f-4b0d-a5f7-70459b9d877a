public with sharing class cmp_CompetitionJudgeCtrl {

    @AuraEnabled(Cacheable=false)
    public static Boolean hasPendingJudge(Id competitionId) {
        try {
            cmp_Judge__c judge = findJudgeForCurrentUser(competitionId);
            return judge != null;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static Boolean approveYourselfAsJudge(Id competitionId, Boolean isApproved) {
        try {
            cmp_Judge__c judge = findJudgeForCurrentUser(competitionId);
            if (judge.ApprovalStatus__c == 'Pending') { // Only Pending statuses might be updated by Judge
                judge.Contact__c = PortalUser.getContactId();
                judge.ApprovalStatus__c = isApproved ? 'Approved' : 'Rejected';
                WS.updateRecord(judge);
            }
            return isApproved;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static String getBookHotelURL() {
        try {
            if (PortalUser.hasRole(PortalUser.Role.JUDGE)) {
                String url = (String) cm_Settings__c.getOrgDefaults().BookHotelURL__c;
                return url;
            }
            return null;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    private static cmp_Judge__c findJudgeForCurrentUser(Id competitionId) {
        User currentUser = [SELECT ContactId, Contact.Email FROM User WHERE Id = :UserInfo.getUserId()];
        List<cmp_Judge__c> judge = (List<cmp_Judge__c>) WS.retrieveRecords(
                'SELECT Id, ApprovalStatus__c FROM cmp_Judge__c WHERE ' +
                        ' Competition__c = :competitionId ' +
                        ' AND ApprovalStatus__c = :approvalStatus ' +
                        ' AND (Contact__c = :contactId OR InvitedByEmail__c = :emailAddress) LIMIT 1',
                new Map<String, Object> {
                        'competitionId' => competitionId,
                        'approvalStatus' => 'Pending',
                        'contactId' => currentUser.ContactId,
                        'emailAddress' => currentUser.Contact.Email
                }
        );
        return judge.isEmpty() ? null : judge[0];
    }
}
public with sharing class cmp_CompetitionsSelectionCtrl {

    public class Response {
        @AuraEnabled
        public Boolean allowMultipleParticipation { get; set; }
        @AuraEnabled
        public Contact competitor { get; set; }
        @AuraEnabled
        public Boolean useLimits { get; set; }
        @AuraEnabled
        public List<Competition> allCompetitions { get; set; }
        @AuraEnabled
        public List<CompetitionOrder> selectedCompetitionOrders { get; set; }
    }

    public class Competition {
        @AuraEnabled
        public Decimal limitOfCompetitors { get; set; }
        @AuraEnabled
        public Decimal remainingCompetitors { get; set; }
        @AuraEnabled
        public cmp_Competition__c competition { get; set; }
    }

    public class CompetitionOrder {
        @AuraEnabled
        public Id competitionId { get; set; }
        @AuraEnabled
        public Integer order { get; set; }
    }

    @AuraEnabled(Cacheable=false)
    public static cmp_CompetitionsSelectionCtrl.Response getCompetitions(Id conferenceId, Id contactId) {
        try {
            Contact theContact = new WSHelper().getContactDetails(contactId);

            List<cmp_Competition__c> competitions = cmp_Selector.getCompetitions(conferenceId, theContact.Division__c);
            String membershipType = getMembershipTypeForSchool(PortalUser.getAccountId());

            List<Competition> resultCompetitions = new List<Competition>();

            Map<Id, Map<String, Decimal>> limits = cmp_CompetitionsSelectionCtrl.getLimits(competitions, membershipType);

            for (cmp_Competition__c competition: competitions) {
                Map<String, Decimal> theLimit = limits.get(competition.Id);
                Competition cmp = new Competition();
                cmp.limitOfCompetitors = theLimit.get('limitOfCompetitors');
                cmp.remainingCompetitors = theLimit.get('remainingCompetitors');
                cmp.competition = competition;
                resultCompetitions.add(cmp);
            }

            List<CompetitionOrder> competitionOrders = getCompetitionOrdersForContact(conferenceId, contactId);
            final Boolean ALLOW_MULTIPLE_PARTICIPATION = cmp_Settings__c.getOrgDefaults().AllowMultipleParticipation__c;
            final Boolean USE_LIMITS = cmp_Settings__c.getOrgDefaults().EnableLimitsForCompetitions__c;

            cmp_CompetitionsSelectionCtrl.Response response = new cmp_CompetitionsSelectionCtrl.Response();
            response.allowMultipleParticipation = ALLOW_MULTIPLE_PARTICIPATION;
            response.competitor = theContact;
            response.useLimits = USE_LIMITS;
            response.allCompetitions = resultCompetitions;
            response.selectedCompetitionOrders = competitionOrders;
            return response;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    public static Object updateParticipantCompetitions(Id contactId, List<CompetitionOrder> orderedCompetitions) {
        if (orderedCompetitions.isEmpty()) {
            return null;
        }

        if (cmp_Settings__c.getOrgDefaults().AllowMultipleParticipation__c == false && orderedCompetitions.size() > 1) {
            throw Error.toLWC('Only 1 competition is available for registration.');
        }

        List<Id> competitionsIds = new List<Id>();
        for (CompetitionOrder competition: orderedCompetitions) {
            competitionsIds.add(competition.competitionId);
        }

        Map<Id, cmp_Competition__c> competitionMap = new WSHelper().getCompetitionMap(competitionsIds);
        Id conferenceId = competitionMap.get(competitionsIds[0]).Conference__c;

        Map<Id, cmp_Participant__c> competitionIdToParticipantMap = new WSHelper().getCompetitionIdToParticipantMap(conferenceId, contactId);

        cm_Participant__c conferenceParticipant = new WSHelper().getConferenceParticipantRecord(conferenceId, contactId);
        List<cmp_Participant__c> participationToInsert = new List<cmp_Participant__c>();
        List<cmp_Participant__c> participationToUpsert = new List<cmp_Participant__c>();

        for (CompetitionOrder cmp: orderedCompetitions) {
            Boolean found = false;
            Id foundId = null;




            for (Id existingCompetitionsId: competitionIdToParticipantMap.keySet()) {
                if (cmp.competitionId == existingCompetitionsId) { // Do not touch
                    foundId = competitionIdToParticipantMap.get(existingCompetitionsId).Id;
                    found = true;
                }
            }

            participationToUpsert.add(new cmp_Participant__c(
                    Id = foundId,
                    ConferenceParticipant__c = conferenceParticipant.Id,
                    Competition__c = cmp.competitionId,
                    Participant__c = contactId,
                    PreferenceOrder__c = cmp.order
            ));
        }

        Set<Id> competitionToDelete = new Set<Id>();
        for (Id existingCompetitionsId: competitionIdToParticipantMap.keySet()) {
            if (!competitionsIds.contains(existingCompetitionsId)) { // Delete
                competitionToDelete.add(existingCompetitionsId);
            }
        }

        List<cmp_Participant__c> participationToDelete = new WSHelper().getParticipants(contactId, competitionToDelete);

        WS.deleteRecords(participationToDelete);
        WS.upsertRecords(participationToUpsert);

        return null;
    }

    public static Map<Id, Map<String, Decimal>> getLimits(List<cmp_Competition__c> competitions, String membershipType) {
        final Boolean USE_LIMITS = Test.isRunningTest() ? true : cmp_Settings__c.getOrgDefaults().EnableLimitsForCompetitions__c;
        final Boolean USE_DISTRICT_LOGIC = Test.isRunningTest() ? true : cmp_Settings__c.getOrgDefaults().UseDistrictLogicInLimits__c;

        Map<Id, Map<String, Decimal>> result = new Map<Id, Map<String, Decimal>>();

        if (!USE_LIMITS) {
            for (cmp_Competition__c competition: competitions) {
                result.put(competition.Id, new Map<String, Decimal> {
                        'limitOfCompetitors' => -1,
                        'remainingCompetitors' => -1
                });
            }

            return result;
        }

        for (cmp_Competition__c competition: competitions) {
            Boolean isState = competition.Type__c == 'State';
            Boolean isDistrict = competition.Type__c == 'District';
            Decimal limitOfCompetitors = 0;

            if (USE_DISTRICT_LOGIC == false || isState) {
                Decimal limitCompetitors = competition.LimitOfCompetitors__c ?? 1000;
                Decimal limitTeams = competition.LimitOfTeams__c ?? 1;

                limitOfCompetitors = competition.IsTeamCompetition__c
                        ? limitCompetitors * limitTeams
                        : limitCompetitors;
            }
            else if (isDistrict && membershipType == 'Direct') {
                Decimal limitDirect = competition.LimitOfCompetitorsDirect__c ?? 1000;
                Decimal limitTeamDirect = competition.LimitOfTeamsDirect__c ?? 100;

                limitOfCompetitors = competition.IsTeamCompetition__c
                        ? limitDirect * limitTeamDirect
                        : limitDirect;
            }
            else if (isDistrict) {
                Decimal limitTPP = competition.LimitOfCompetitorsTPP__c ?? 1000;
                Decimal limitTeamTPP = competition.LimitOfTeamsTPP__c ?? 100;

                limitOfCompetitors = competition.IsTeamCompetition__c
                        ? limitTPP * limitTeamTPP
                        : limitTPP;
            }

            Integer approvedCompetitors = 0;
            Integer pendingCompetitors = 0;

            for (cmp_Participant__c participant: competition.CompetitionParticipants__r) {
                String registrationStatus = participant.ConferenceParticipant__r.RegistrationStatus__c;
                if (registrationStatus == 'Approved') {
                    approvedCompetitors++;
                } else if (registrationStatus == 'Pending approval') {
                    pendingCompetitors++;
                }
            }

            result.put(competition.Id, new Map<String, Decimal> {
                    'limitOfCompetitors' => limitOfCompetitors,
                    'remainingCompetitors' => limitOfCompetitors - approvedCompetitors,
                    'pendingCompetitors' => pendingCompetitors
            });
        }

        return result;
    }

    private static List<CompetitionOrder> getCompetitionOrdersForContact(Id conferenceId, Id contactId) {
        List<cmp_Participant__c> participants = cmp_Selector.getParticipantCompetitions(contactId, conferenceId);
        List<CompetitionOrder> result = new List<CompetitionOrder>();
        for (cmp_Participant__c participant : participants) {
            CompetitionOrder CO = new CompetitionOrder();
            CO.competitionId = participant.Competition__c;
            CO.order = Integer.valueOf(participant.PreferenceOrder__c);
            result.add(CO);
        }
        return result;
    }

    public static String getMembershipTypeForSchool(Id accountId) {
        return Test.isRunningTest() ? 'Direct' : [SELECT MembershipType__c FROM Account WHERE Id = :accountId].MembershipType__c;
    }

    without sharing class WSHelper {

        public List<cmp_Participant__c> getParticipants(Id contactId, Set<Id> competitionsIds) {
            return [
                    SELECT Id
                    FROM cmp_Participant__c
                    WHERE Participant__c = :contactId
                    AND Competition__c IN :competitionsIds
            ];
        }

        public Contact getContactDetails(Id contactId) {
            return [SELECT Division__c, IsFreshman__c FROM Contact WHERE Id = :contactId];
        }

        public cm_Participant__c getConferenceParticipantRecord(Id conferenceId, Id contactId) {
            return [SELECT Id FROM cm_Participant__c WHERE Contact__c = :contactId AND Conference__c = :conferenceId];
        }

        public Map<Id, cmp_Participant__c> getCompetitionIdToParticipantMap(Id conferenceId, Id contactId) {
            Map<Id, cmp_Participant__c> result = new Map<Id, cmp_Participant__c>();
            List<cmp_Participant__c> participants = [
                    SELECT Id, Competition__c
                    FROM cmp_Participant__c
                    WHERE Competition__r.Conference__c = :conferenceId
                    AND Participant__c = :contactId
            ];
            for (cmp_Participant__c p: participants) {
                result.put(p.Competition__c, p);
            }
            return result;
        }

        public Map<Id, cmp_Competition__c> getCompetitionMap(List<Id> competitionsIds) {
            return new Map<Id, cmp_Competition__c>([
                    SELECT Id, Code__c, Conference__c FROM cmp_Competition__c WHERE Id IN :competitionsIds
            ]);
        }

    }

}
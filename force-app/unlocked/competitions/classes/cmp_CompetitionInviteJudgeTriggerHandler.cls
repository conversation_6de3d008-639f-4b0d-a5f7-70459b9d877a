public with sharing class cmp_CompetitionInviteJudgeTriggerHandler {

    public static void updateContactByJudgeRole(Set<Id> contactIds) {
        List<Contact> contacts = (List<Contact>) WS.retrieveRecords(
                'SELECT Id, PortalRole__c FROM Contact WHERE Id IN :contactIds AND PortalRole__c EXCLUDES (\'Judge\')',
                new Map<String, Object> {
                        'contactIds' => contactIds
                }
        );
        for (Contact theContact: contacts) {
            theContact.PortalRole__c = theContact.PortalRole__c + ';Judge';
        }
        WS.updateRecords(contacts);
    }

    public static void sendInviteToJudges(Id competitionId, Set<Id> contactIds) {
        if (contactIds.isEmpty()) {
            return;
        }

        List<Contact> contacts = (List<Contact>) WS.retrieveRecords(
                'SELECT Email FROM Contact WHERE Id IN :contactId',
                new Map<String, Object> { 'contactId' => contactIds }
        );
        List<String> emails = new List<String>();
        for (Contact theContact: contacts) {
            emails.add(theContact.Email);
        }

        String templateName = (String) cm_Settings__c.getOrgDefaults().FoundJudgeInviteEmailTemplateName__c;
        sendInvite(templateName, emails, competitionId);
    }

    public static void sendInviteToJudges(Id competitionId, List<String> emails) {
        if (emails.isEmpty()) {
            return;
        }
        String templateName = (String) cm_Settings__c.getOrgDefaults().JudgeInviteEmailTemplateName__c;
        sendInvite(templateName, emails, competitionId);
    }

    private static void sendInvite(String templateName, List<String> emails, Id competitionId) {
        String orgWideId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;

        EmailTemplate testTemplate = new EmailTemplate(
                Subject = '',
                HtmlValue = '',
                Body = ''
        );

        EmailTemplate template = cmp_Selector.getEmailTemplate(templateName);
        template = Test.isRunningTest() ? testTemplate : template;

        String htmlBody = template.HtmlValue?.replaceAll('<COMPETITION_ID>', competitionId);
        String textBody = template.Body?.replaceAll('<COMPETITION_ID>', competitionId);
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setToAddresses(emails);
        mail.setSubject(template.Subject);
        mail.setPlainTextBody(textBody);
        mail.setHtmlBody(htmlBody);
        mail.setOrgWideEmailAddressId(orgWideId);

        if (orgWideId != null && template != null) {
            Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
        }
    }


}
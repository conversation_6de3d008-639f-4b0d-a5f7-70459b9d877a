public with sharing class cmp_ParticipantTriggerHandler {

    public static void updateIndividualNumber(List<cmp_Participant__c> newParticipants, Boolean isBeforeInsert) {
        Set<Id> competitionIds = new Set<Id>();
        Set<Id> accountIds = new Set<Id>();
        for (cmp_Participant__c participant : newParticipants) {
            competitionIds.add(participant.Competition__c);
            accountIds.add(participant.SchoolId__c);
        }

        // Retrieve all approved participants for the same competition and account
        // ALL ROWS to avoid situation when newly added participant will get the number of deleted participant
        // For example: [1, 2, .., 4] where '..' is deleted participant
        List<cmp_Participant__c> participantsToCount = [
                SELECT Id, SchoolId__c, Competition__c
                FROM cmp_Participant__c
                WHERE Competition__c IN :competitionIds
                AND SchoolId__c IN :accountIds
                AND IndividualNumber__c != NULL
                ALL ROWS
        ];

        // Count participants for each competition and account
        Map<String, Integer> competitionAndAccountToParticipants = new Map<String, Integer>();
        for (cmp_Participant__c participant : participantsToCount) {
            Id competitionId = participant.Competition__c;
            Id accountId = participant.SchoolId__c;
            String key = competitionId + ':' + accountId;

            Integer participantCount = competitionAndAccountToParticipants.get(key) ?? 0;
            competitionAndAccountToParticipants.put(key, participantCount + 1);
        }

        // Update only for approved participants
        List<cmp_Participant__c> participantsToUpdate = new List<cmp_Participant__c>();
        for (cmp_Participant__c participant : newParticipants) {
            if (participant.IsApprovedForConference__c == false) {
                continue;
            }

            Id competitionId = participant.Competition__c;
            Id accountId = participant.SchoolId__c;
            String key = competitionId + ':' + accountId;

            Integer participantCount = competitionAndAccountToParticipants.get(key);
            if (participantCount == null) {
                participantCount = 0;
                competitionAndAccountToParticipants.put(key, 1);
            } else {
                competitionAndAccountToParticipants.put(key, participantCount + 1); // increment not inserted yet participant
            }

            participant.IndividualNumber__c = participantCount + 1;
            participantsToUpdate.add(participant);
        }

        if (isBeforeInsert == false) {
            WS.updateRecords(participantsToUpdate);
        }
    }

    public static void assignAssessments(List<cmp_Participant__c> records) {
        // Fetch participants for approved conferences
        List<cmp_Participant__c> participants = fetchValidParticipants(records);
        if (participants.isEmpty()) {
            return;
        }

        // Map participant contacts to user IDs
        Map<Id, Id> contactIdToUserId = mapContactsToUsers(participants);

        // Build a structure linking user/competition to the as_Result__c records to be inserted
        Map<Id, Map<Id, List<as_Result__c>>> participantToResultMap = buildResultMap(participants, contactIdToUserId);

        // Flatten those results into a single list and insert
        List<as_Result__c> resultsToInsert = flattenResults(participantToResultMap);
        insertResults(resultsToInsert);

        // Re-query the inserted results along with their competition relationship
        Map<Id, as_Result__c> resultsMap = fetchResultsWithCompetition(resultsToInsert);

        // Build the as_ResultGroup__c records from the newly inserted results
        List<as_ResultGroup__c> groupsToInsert = buildResultGroups(participantToResultMap, resultsMap);
        insert groupsToInsert;
    }

    public static void deleteAssignments(List<cmp_Participant__c> records) {
        if (records.isEmpty()) {
            return;
        }

        // 1) Collect relevant (Contact, Competition) pairs from the deleted Participants
        //    so we can bulk-query related as_Result__c and as_ResultGroup__c.
        Set<Id> contactIds = new Set<Id>();
        Set<Id> competitionIds = new Set<Id>();
        for (cmp_Participant__c p : records) {
            // We only handle records with non-null Participant__c and Competition__c
            if (p.Participant__c != null && p.Competition__c != null) {
                contactIds.add(p.Participant__c);
                competitionIds.add(p.Competition__c);
            }
        }
        if (contactIds.isEmpty() || competitionIds.isEmpty()) {
            return;
        }

        // 2) Query all as_Result__c records that match the (Contact, Competition) pairs
        List<as_Result__c> resultsToDelete = [
                SELECT Id, Contact__c, cmp_Competition__c
                FROM as_Result__c
                WHERE Contact__c IN :contactIds
                AND cmp_Competition__c IN :competitionIds
        ];

        delete resultsToDelete;

        // 3) Now check as_ResultGroup__c records that might reference those Results
        //    We only need to query ResultGroups whose (Contact__c, cmp_Competition__c)
        //    match what was in 'records' because that’s where the references live.
        List<as_ResultGroup__c> groups = [
                SELECT Id,
                        Contact__c,
                        cmp_Competition__c,
                        ResultTechnical__c,
                        ResultReadiness__c,
                        ResultEmployability__c
                FROM as_ResultGroup__c
                WHERE Contact__c IN :contactIds
                AND cmp_Competition__c IN :competitionIds
        ];

        // 4) Determine which groups no longer have any references
        List<as_ResultGroup__c> groupsToDelete = new List<as_ResultGroup__c>();
        for (as_ResultGroup__c theGroup : groups) {
            // If all three references are null, remove that group
            if (theGroup.ResultTechnical__c == null
                    && theGroup.ResultReadiness__c == null
                    && theGroup.ResultEmployability__c == null) {
                groupsToDelete.add(theGroup);
            }
        }

        delete groupsToDelete;
    }

    // Fetch participants who have been approved for conference and are students
    private static List<cmp_Participant__c> fetchValidParticipants(List<cmp_Participant__c> records) {
        return [
                SELECT Id,
                        Participant__c,
                        Competition__r.AssessmentTech__c,
                        Competition__r.AssessmentSafety__c,
                        Competition__r.AssessmentEmployability__c,
                        Competition__r.Id
                FROM cmp_Participant__c
                WHERE Id IN :records
                AND IsApprovedForConference__c = TRUE
                AND Participant__r.PortalRole__c INCLUDES ('Student')
        ];
    }

    // Map all participant contacts to their corresponding user ID
    private static Map<Id, Id> mapContactsToUsers(List<cmp_Participant__c> participants) {
        Set<Id> contactIds = new Set<Id>();
        for (cmp_Participant__c participant : participants) {
            contactIds.add(participant.Participant__c);
        }

        List<User> users = [SELECT ContactId FROM User WHERE ContactId IN :contactIds];
        Map<Id, Id> contactIdToUserId = new Map<Id, Id>();

        for (User theUser : users) {
            contactIdToUserId.put(theUser.ContactId, theUser.Id);
        }
        return contactIdToUserId;
    }

    // Build a map from userId -> (competitionId -> list of as_Result__c)
    private static Map<Id, Map<Id, List<as_Result__c>>> buildResultMap(
            List<cmp_Participant__c> participants,
            Map<Id, Id> contactIdToUserId
    ) {
        Map<Id, Map<Id, List<as_Result__c>>> participantToResultMap = new Map<Id, Map<Id, List<as_Result__c>>>();

        for (cmp_Participant__c participant : participants) {
            Id userId = contactIdToUserId.get(participant.Participant__c);
            if (userId == null) {
                // Skip if user doesn't exist
                continue;
            }

            Id competitionId = participant.Competition__r.Id;

            // Initialize nested structures if needed
            if (!participantToResultMap.containsKey(userId)) {
                participantToResultMap.put(userId, new Map<Id, List<as_Result__c>>());
            }
            if (!participantToResultMap.get(userId).containsKey(competitionId)) {
                participantToResultMap.get(userId).put(competitionId, new List<as_Result__c>());
            }

            List<as_Result__c> resultsList = participantToResultMap.get(userId).get(competitionId);

            // Create one as_Result__c for each relevant assessment field
            if (participant.Competition__r.AssessmentTech__c != null) {
                resultsList.add(new as_Result__c(
                        Participant__c = userId,
                        Contact__c = participant.Participant__c,
                        cmp_Competition__c = competitionId,
                        cmp_Participant__c = participant.Id,
                        Assessment__c = participant.Competition__r.AssessmentTech__c
                ));
            }
            if (participant.Competition__r.AssessmentSafety__c != null) {
                resultsList.add(new as_Result__c(
                        Participant__c = userId,
                        Contact__c = participant.Participant__c,
                        cmp_Competition__c = competitionId,
                        cmp_Participant__c = participant.Id,
                        Assessment__c = participant.Competition__r.AssessmentSafety__c
                ));
            }
            if (participant.Competition__r.AssessmentEmployability__c != null) {
                resultsList.add(new as_Result__c(
                        Participant__c = userId,
                        Contact__c = participant.Participant__c,
                        cmp_Competition__c = competitionId,
                        cmp_Participant__c = participant.Id,
                        Assessment__c = participant.Competition__r.AssessmentEmployability__c
                ));
            }
        }
        return participantToResultMap;
    }

    // Flatten the nested map into a single list of as_Result__c for DML
    private static List<as_Result__c> flattenResults(Map<Id, Map<Id, List<as_Result__c>>> participantToResultMap) {
        List<as_Result__c> resultsToInsert = new List<as_Result__c>();
        for (Id userId : participantToResultMap.keySet()) {
            for (Id competitionId : participantToResultMap.get(userId).keySet()) {
                resultsToInsert.addAll(participantToResultMap.get(userId).get(competitionId));
            }
        }
        return resultsToInsert;
    }

    // Insert the as_Result__c records
    private static void insertResults(List<as_Result__c> resultsToInsert) {
        insert resultsToInsert;
    }

    // Re-query inserted as_Result__c records, including competition fields
    private static Map<Id, as_Result__c> fetchResultsWithCompetition(List<as_Result__c> resultsToInsert) {
        return new Map<Id, as_Result__c>([
                SELECT Id,
                        Assessment__c,
                        cmp_Competition__r.AssessmentTech__c,
                        cmp_Competition__r.AssessmentSafety__c,
                        cmp_Competition__r.AssessmentEmployability__c
                FROM as_Result__c
                WHERE Id IN :resultsToInsert
        ]);
    }

    // Build a list of as_ResultGroup__c from the inserted results
    private static List<as_ResultGroup__c> buildResultGroups(
            Map<Id, Map<Id, List<as_Result__c>>> participantToResultMap,
            Map<Id, as_Result__c> resultsMap
    ) {
        List<as_ResultGroup__c> groups = new List<as_ResultGroup__c>();

        for (Id userId : participantToResultMap.keySet()) {
            Map<Id, List<as_Result__c>> competitionResultsMap = participantToResultMap.get(userId);
            for (Id competitionId : competitionResultsMap.keySet()) {
                List<as_Result__c> resultsList = competitionResultsMap.get(competitionId);
                if (resultsList.isEmpty()) {
                    continue;
                }

                as_Result__c techResult = null;
                as_Result__c safetyResult = null;
                as_Result__c employabilityResult = null;

                // Identify which inserted record is which
                for (as_Result__c result : resultsList) {
                    as_Result__c resultWithCompetition = resultsMap.get(result.Id);
                    if (resultWithCompetition == null) {
                        continue;
                    }

                    if (resultWithCompetition.Assessment__c == resultWithCompetition.cmp_Competition__r.AssessmentTech__c) {
                        techResult = resultWithCompetition;
                    } else if (resultWithCompetition.Assessment__c == resultWithCompetition.cmp_Competition__r.AssessmentSafety__c) {
                        safetyResult = resultWithCompetition;
                    } else if (resultWithCompetition.Assessment__c == resultWithCompetition.cmp_Competition__r.AssessmentEmployability__c) {
                        employabilityResult = resultWithCompetition;
                    }
                }

                // Create a new as_ResultGroup__c record for this competition
                groups.add(new as_ResultGroup__c(
                        Contact__c = resultsList[0].Contact__c,
                        cmp_Competition__c = competitionId,
                        ResultTechnical__c = techResult?.Id,
                        ResultReadiness__c = safetyResult?.Id,
                        ResultEmployability__c = employabilityResult?.Id
                ));
            }
        }
        return groups;
    }
}

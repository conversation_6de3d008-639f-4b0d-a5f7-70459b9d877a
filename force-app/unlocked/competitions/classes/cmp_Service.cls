public with sharing class cmp_Service {
    @TestVisible
    private static String JUDGE_OR_TC_NOT_FOUND = 'Record not found. [1]';
    @TestVisible
    private static String JUDGE_OR_TC_NOT_INVITED = 'Record not found. [2]';

    @AuraEnabled(Cacheable=true)
    public static cmp_Service.CompetitionDTO getCompetitionDetails(Id recordId) {
        try {
            checkRoles(); // Throws an exception

            cmp_Competition__c competition = cmp_Selector.getCompetition(recordId);
            User currentUser = [SELECT ContactId, Contact.Email FROM User WHERE Id = :UserInfo.getUserId()];

            Boolean isJudgeOnCompetition = false;
            Boolean isTCOnCompetition = false;

            for (cmp_Judge__c judge: competition.CompetitionJudges__r) {
                if (judge.Contact__c == currentUser.ContactId) {
                    isJudgeOnCompetition = true;
                }
            }

            for (cmp_Chairperson__c TC: competition.CompetitionChairpersons__r) {
                if (TC.Chairperson__c == currentUser.ContactId) {
                    isTCOnCompetition = true;
                }
            }


            Boolean showFieldsForTech = competition.Conference__r.DisplayCompetitorInfoForTech__c;

            if (isJudgeOnCompetition || (isTCOnCompetition && showFieldsForTech == false)) {
                for (cmp_Participant__c participant: competition.CompetitionParticipants__r) {
                    if (participant.Participant__c != null) {
                        participant.Participant__c = null;
                        participant.Participant__r.Id = null;
                        participant.Participant__r.FirstName = '-';
                        participant.Participant__r.LastName = '-';
                        participant.Participant__r.Division__c = '-';
                        participant.Participant__r.Account.Name = '-';
                    }
                }
            }


            for (cmp_Judge__c judge: competition.CompetitionJudges__r) {
                if (judge.Contact__c == currentUser.ContactId || judge.InvitedByEmail__c == currentUser.Contact.Email) {
                    return new CompetitionDTO(competition, judge.ApprovalStatus__c == 'Approved'); // Retrieve DTO for judge, show details only if Judge is approved
                }
            }

            if (isTCOnCompetition) {
                return new CompetitionDTO(competition, true); // Retrieve DTO for chairperson, show details
            }

            throw Error.toLWC(JUDGE_OR_TC_NOT_INVITED);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    public static Map<Id, cm_MembersManagementDTO> getCompetitionMembers(Id conferenceId, Boolean showOnlyApproved) {
        List<cm_Participant__c> participants = cm_Selector.getConferenceParticipants(
                conferenceId,
                showOnlyApproved
        );

        Set<Id> participantIds = new Set<Id>();
        for (cm_Participant__c participant: participants) {
            participantIds.add(participant.Contact__c);
        }

        List<cmp_Participant__c> competitionParticipants = [
                SELECT Id,
                        Competition__r.Name,
                        Participant__c,
                        Competition__r.CompetitionFee__c
                FROM cmp_Participant__c
                WHERE Competition__r.Conference__c = :conferenceId
                AND Participant__c IN :participantIds
        ];

        Map<Id, List<String>> contactIdToCompetitions = new Map<Id, List<String>>();
        for(cmp_Participant__c cmpParticipant: competitionParticipants) {
            List<String> competitionNames = contactIdToCompetitions.get(cmpParticipant.Participant__c);
            if (competitionNames == null) {
                contactIdToCompetitions.put(cmpParticipant.Participant__c, new List<String> {
                        cmpParticipant.Competition__r.Name + ' ($' + cmpParticipant.Competition__r.CompetitionFee__c + ')'
                });
            } else {
                competitionNames.add(cmpParticipant.Competition__r.Name  + ' ($' + cmpParticipant.Competition__r.CompetitionFee__c + ')');
                contactIdToCompetitions.put(cmpParticipant.Participant__c, competitionNames);
            }
        }

        Map<Id, cm_MembersManagementDTO> conferenceDTOList = new Map<Id, cm_MembersManagementDTO>();
        for (cm_Participant__c participant : participants) {
            conferenceDTOList.put(
                    participant.Contact__c,
                    new cm_MembersManagementDTO(participant, contactIdToCompetitions.get(participant.Contact__c))
            );
        }

        return conferenceDTOList;
    }

    private static void checkRoles() {
        // Check if technical chair or judge has access to the record
        Boolean hasTCRole = PortalUser.hasRole(PortalUser.Role.TECHNICAL_CHAIR);
        Boolean hasJudgeRole = PortalUser.hasRole(PortalUser.Role.JUDGE);

        if (!hasTCRole && !hasJudgeRole) {
            throw Error.toLWC(JUDGE_OR_TC_NOT_FOUND);
        }
    }

    public class CompetitionDTO {
        @AuraEnabled
        public cmp_Competition__c competition;

        @AuraEnabled
        public Boolean showDetails;

        public CompetitionDTO(cmp_Competition__c competition, Boolean showDetails) {
            this.competition = competition;
            this.showDetails = showDetails;
        }
    }

}
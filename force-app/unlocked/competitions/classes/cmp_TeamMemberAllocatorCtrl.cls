public with sharing class cmp_TeamMemberAllocatorCtrl {

    @AuraEnabled
    public static Response init(Id competitionId) {
        try {
            cmp_Competition__c competition = new WSHelper().getCompetition(competitionId, PortalUser.getAccountId());

            if (competition.IsTeamCompetition__c == false) {
                throw Error.toLWC('The competition must be marked as Team Competition.');
            }

            String membershipType = [SELECT MembershipType__c FROM Account WHERE Id = :PortalUser.getAccountId()].MembershipType__c;

            Decimal numberOfTeams = getNumberOfTeams(competition, membershipType);
            Decimal numberOfParticipants = competition.CompetitionParticipants__r.size();
            Decimal numberOfAvailableTeams = numberOfParticipants >= numberOfTeams ? numberOfTeams : numberOfParticipants;

            Response response = new Response();

            response.competition = competition;

            response.participants = competition.CompetitionParticipants__r;

            response.hasTeamRoles = response.availableRoles != null;

            response.availableForEdit = competition.Conference__r.RegistrationDeadline__c >= System.today();

            response.numberOfTeams = numberOfAvailableTeams;

            response.competitorsPerTeam = getNumberOfParticipantsPerTeam(competition, membershipType);
            response.availableRoles = getRoles(competition, response.competitorsPerTeam);

            return response;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    private static List<String> getRoles(cmp_Competition__c competition, Decimal competitorsPerTeam) {
        if (competition.IsTeamCompetition__c) {
            if (competition.TeamCompetitionType__c == 'ROLES') {
                return competition.TeamRoles__c?.split(' ');
            }
            List<String> numberList = new List<String>();
            for (Integer i = 1; i <= competitorsPerTeam; i++) {
                numberList.add(String.valueOf(i));
            }
            return numberList;
        }
        return new List<String>();
    }

    private static Decimal getNumberOfTeams(cmp_Competition__c competition, String membershipType) {
        final Boolean USE_DISTRICT_LOGIC = Test.isRunningTest() ? true : cmp_Settings__c.getOrgDefaults().UseDistrictLogicInLimits__c;

        if (USE_DISTRICT_LOGIC == false || competition.Type__c == 'State') {
            return competition.LimitOfTeams__c;
        }
        else if (competition.Type__c == 'District') {
            return membershipType == 'TPP'
                    ? competition.LimitOfTeamsTPP__c
                    : competition.LimitOfTeamsDirect__c;
        }
        return 0;
    }

    private static Decimal getNumberOfParticipantsPerTeam(cmp_Competition__c competition, String membershipType) {
        final Boolean USE_DISTRICT_LOGIC = Test.isRunningTest() ? true : cmp_Settings__c.getOrgDefaults().UseDistrictLogicInLimits__c;

        if (USE_DISTRICT_LOGIC == false || competition.Type__c == 'State') {
            return competition.LimitOfCompetitors__c;
        }
        else if (competition.Type__c == 'District') {
            return membershipType == 'TPP'
                    ? competition.LimitOfCompetitorsTPP__c
                    : competition.LimitOfCompetitorsDirect__c;
        }

        return 0;
    }

    @AuraEnabled
    public static void save(Id competitionId, String jsonMap) {
        try {
            List<Competitor> competitorsList = (List<Competitor>) JSON.deserialize(jsonMap, List<Competitor>.class);

            if (competitorsList.size() == 0) {
                return ;
            }

            // 0. Get list of all records
            cmp_Competition__c theCompetition = new WSHelper().getCompetition(competitionId, PortalUser.getAccountId());

            Map<Id, String> participantToSchoolCodeMap = getParticipantToSchoolCodeMap(theCompetition.CompetitionParticipants__r);

            // 1. Update records from jsonMap
            for (cmp_Participant__c participant: theCompetition.CompetitionParticipants__r) {
                Competitor theCompetitor = null;

                for (Competitor cmp: competitorsList) {
                    if (cmp.id == participant.Id) {
                        theCompetitor = cmp;
                    }
                }

                if (theCompetitor != null) {
                    String contestantNumber = '';
                    Integer teamN = (theCompetitor.team ?? 1);
                    if (theCompetition.IsTeamCompetition__c) {
                        String teamNumber = teamN < 10 ? '0' + teamN : teamN + '';
                        contestantNumber = theCompetition.Code__c + participantToSchoolCodeMap.get(theCompetitor.id) + teamNumber;
                    }

                    participant.TeamId__c = String.valueOf(theCompetitor.team);
                    participant.Role__c = theCompetitor.role;
                    participant.ContestantNumber__c = contestantNumber + theCompetitor.role;
                    participant.ContestantCode__c = contestantNumber;
                } else {
                    participant.TeamId__c = '';
                    participant.Role__c = '';
                    participant.ContestantNumber__c = '';
                    participant.ContestantCode__c = '';
                }
            }

            WS.updateRecords(theCompetition.CompetitionParticipants__r);

        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    static private Map<Id, String> getParticipantToSchoolCodeMap(List<cmp_Participant__c> participants) {
        Map<Id, String> result = new Map<Id, String>();
        for (cmp_Participant__c participant: participants) {
            result.put(participant.Id, participant.Participant__r.Account.cmp_SchoolCode__c);
        }
        return result;
    }

    public class Response {
        @AuraEnabled
        public cmp_Competition__c competition { get; set; }
        @AuraEnabled
        public List<String> availableRoles { get; set; }
        @AuraEnabled
        public Boolean hasTeamRoles { get; set; }
        @AuraEnabled
        public Boolean availableForEdit { get; set; }
        @AuraEnabled
        public Decimal numberOfTeams { get; set; }
        @AuraEnabled
        public Decimal competitorsPerTeam { get; set; }
        @AuraEnabled
        public List<cmp_Participant__c> participants { get; set; }
    }

    public without sharing class WSHelper {
        public cmp_Competition__c getCompetition(Id competitionId, Id participantAccount) {
            return [
                    SELECT Id,
                            Name,
                            TeamRoles__c,
                            Type__c,
                            Code__c,
                            Subtype__c,
                            TeamCompetitionType__c,
                            LimitOfTeams__c,
                            LimitOfTeamsDirect__c,
                            LimitOfTeamsTPP__c,
                            LimitOfCompetitorsTPP__c,
                            LimitOfCompetitorsDirect__c,
                            LimitOfCompetitors__c,
                            IsTeamCompetition__c,
                            Conference__r.RegistrationDeadline__c,
                    (
                            SELECT Id, Participant__r.Name, TeamId__c, Role__c, Participant__r.Account.cmp_SchoolCode__c
                            FROM CompetitionParticipants__r
                            WHERE Participant__r.AccountId = :participantAccount
                            AND ConferenceParticipant__r.RegistrationStatus__c = 'Approved'
                    )
                    FROM cmp_Competition__c
                    WHERE Id = :competitionId
            ];
        }
    }

    public class Competitor {
        public String id { get; set; }
        public String name { get; set; }
        public Integer team { get; set; }
        public String role { get; set; }
    }

}
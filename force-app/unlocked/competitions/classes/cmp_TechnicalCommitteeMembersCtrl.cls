public with sharing class cmp_TechnicalCommitteeMembersCtrl {
    private static String STUDENT_JUDGE_MESSAGE = 'An email is already registered in the system and associated with the Student portal role.' +
            ' Please note that students are ineligible to serve as competition judges.\n' +
            'Please, enter another email or contact the support.';

    @AuraEnabled(Cacheable=true)
    public static cmp_Service.CompetitionDTO getCompetitionDetails(Id recordId) {
        try {
            return cmp_Service.getCompetitionDetails(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static String getBookHotelURL() {
        try {
            if (PortalUser.hasRole(PortalUser.Role.JUDGE)) {
                String url = (String) cm_Settings__c.getOrgDefaults().BookHotelURL__c;
                return url;
            }
            return null;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void createJudgeInvite(Id competitionId, Id judgeId) {
        try {
            createJudgeRecord(competitionId, judgeId, null);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void createJudgeEmailInvite(Id competitionId, String email) {
        try {
            List<Contact> contacts = WS.retrieveRecords(
                    'SELECT Id, PortalRole__c FROM Contact WHERE Email = :email',
                    new Map<String, Object> { 'email' => email }
            );

            if (!contacts.isEmpty()) {
                String portalRole = contacts[0].PortalRole__c;
                if (portalRole.contains('Student') || portalRole.contains('Middle School Member')) {
                    throw Error.exception(STUDENT_JUDGE_MESSAGE);
                }

                createJudgeRecord(competitionId, contacts[0].Id, null); // Send email to registered user
            }
            else {
                createJudgeRecord(competitionId, null, email); // user is not in the system
            }
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void sendEmail(Id competitionId, String subject, String body) {
        try {
            cmp_Competition__c competition = cmp_Selector.getCompetition(competitionId);

            List<String> emails = new List<String>();
            for (cmp_Judge__c judge: competition.CompetitionJudges__r) {
                if (judge.ApprovalStatus__c == 'Approved') {
                    emails.add(judge.Contact__r.Email);
                }
            }

            if (emails.isEmpty()) {
                return;
            }

            String orgWideId = (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.OrgWideEmailId__c);
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            mail.setOrgWideEmailAddressId(orgWideId);
            mail.setToAddresses(emails);
            mail.setSubject(subject);
            mail.setHtmlBody(body);
            Messaging.sendEmail(new List<Messaging.Email>{ mail });
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=true)
    public static Boolean isApprovedJudge(Id recordId) {
        try {
            Boolean hasRole = PortalUser.hasRole(PortalUser.Role.JUDGE);
            cmp_Competition__c competition = cmp_Selector.getCompetition(recordId);
            User currentUser = [SELECT ContactId, Contact.Email FROM User WHERE Id = :UserInfo.getUserId()];
            for (cmp_Judge__c judge: competition.CompetitionJudges__r) {
                if (
                        judge.ApprovalStatus__c == 'Approved' &&
                                (judge.Contact__c == currentUser.ContactId || judge.InvitedByEmail__c == currentUser.Contact.Email)
                ) {
                    return hasRole;
                }
            }

            return false;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    private static void createJudgeRecord(Id competitionId, Id judgeId, String invitedByEmail) {
        WS.insertRecord(new cmp_Judge__c(
                Competition__c = competitionId,
                Contact__c = judgeId,
                InvitedByEmail__c = invitedByEmail
        ));
    }

}
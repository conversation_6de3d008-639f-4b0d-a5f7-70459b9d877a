public with sharing class cmp_CompetitionDetailsCtrl {
    @AuraEnabled(Cacheable=true)
    public static cmp_Competition__c getCompetitionDetails(Id recordId) {
        try {
            // Check if technical chair or judge has access to the record
            Boolean hasTCRole = PortalUser.hasRole(PortalUser.Role.TECHNICAL_CHAIR);
            Boolean hasJudgeRole = PortalUser.hasRole(PortalUser.Role.JUDGE);
            User currentUser = [
                    SELECT ContactId, Contact.Email
                    FROM User
                    WHERE Id = :UserInfo.getUserId()
            ];

            if (!hasTCRole && !hasJudgeRole) {
                throw Error.toLWC('Record not found. [1]');
            }

            cmp_Competition__c competition = cmp_Selector.getCompetition(recordId);

            for (cmp_Judge__c judge: competition.CompetitionJudges__r) {
                if (judge.Contact__c == currentUser.ContactId || judge.InvitedByEmail__c == currentUser.Contact.Email) {
                    return competition;
                }
            }

            for (cmp_Chairperson__c TC: competition.CompetitionChairpersons__r) {
                if (TC.Chairperson__c == currentUser.ContactId) {
                    return competition;
                }
            }

            throw Error.toLWC('Record not found. [2]');
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=true)
    public static Boolean isTechnicalChair(Id recordId) {
        try {
            Boolean hasRole = PortalUser.hasRole(PortalUser.Role.TECHNICAL_CHAIR);
            cmp_Competition__c competition = cmp_Selector.getCompetition(recordId);
            User currentUser = [
                    SELECT ContactId, Contact.Email
                    FROM User
                    WHERE Id = :UserInfo.getUserId()
            ];
            for (cmp_Chairperson__c TC: competition.CompetitionChairpersons__r) {
                if (TC.Chairperson__c == currentUser.ContactId) {
                    return hasRole;
                }
            }

            return false;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=true)
    public static Boolean isApprovedJudge(Id recordId) {
        try {
            Boolean hasRole = PortalUser.hasRole(PortalUser.Role.JUDGE);
            cmp_Competition__c competition = cmp_Selector.getCompetition(recordId);
            User currentUser = [SELECT ContactId, Contact.Email FROM User WHERE Id = :UserInfo.getUserId()];
            for (cmp_Judge__c judge: competition.CompetitionJudges__r) {
                if (
                        judge.ApprovalStatus__c == 'Approved' &&
                        (judge.Contact__c == currentUser.ContactId || judge.InvitedByEmail__c == currentUser.Contact.Email)
                ) {
                    return hasRole;
                }
            }

            return false;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

}
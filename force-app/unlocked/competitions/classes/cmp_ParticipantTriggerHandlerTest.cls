@IsTest
private class cmp_ParticipantTriggerHandlerTest {

    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');

            PortalUser.create(leadAdvisor);

            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            student.Division__c = 'Middle School';
            PortalUser.create(student);

            as_Assessment__c assessment = createAssessments();

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cm_Participant__c cmParticipant = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = student.Id,
                    RegistrationStatus__c = 'Approved'
            );
            insert cmParticipant;

            cmp_Competition__c competition = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School',
                    AssessmentSafety__c = assessment.Id
            );
            insert competition;

            cmp_Participant__c participant = new cmp_Participant__c(
                    Competition__c = competition.Id,
                    Participant__c = student.Id,
                    ConferenceParticipant__c = cmParticipant.Id
            );
            insert participant;
        }
    }

    static as_Assessment__c createAssessments() {
        Map<String, Map<String, Boolean>> questionAnswers = new Map<String, Map<String, Boolean>>();

        questionAnswers.put(
                'What is the capital of Iceland?',
                new Map<String, Boolean> {
                        'Oslo' => false,
                        'Reykjavik' => true,
                        'Helsinki' => false,
                        'Dublin' => false
                }
        );

        as_Assessment__c assessment = new as_Assessment__c(
                Name = 'General education assessment',
                PassCode__c = '12345',
                TimeToComplete__c = 60,
                Description__c = 'General education assessment involves evaluating students\' overall understanding ' +
                        'and proficiency in foundational subjects such as mathematics, language arts, and sciences. '
        );
        insert assessment;

        for (String questionContent: questionAnswers.keySet()) {
            as_Question__c question = new as_Question__c(
                    Assessment__c = assessment.Id,
                    Title__c = questionContent,
                    Score__c = 5
            );
            insert question;

            List<as_Answer__c> answers = new List<as_Answer__c>();
            for (String answerContent: questionAnswers.get(questionContent).keySet()) {
                answers.add(new as_Answer__c(
                        Question__c = question.Id,
                        Body__c = answerContent,
                        IsCorrect__c = questionAnswers.get(questionContent).get(answerContent)
                ));
            }
            insert answers;
        }

        return assessment;
    }

    @IsTest
    static void testBehavior() {
    }
}
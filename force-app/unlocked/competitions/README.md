Draft:

Community Sharing Set - Manual SetUp (To allow user to edit Contact Records during registration)
You need to create the Sharing Set on the Communities. 
If you go to `Setup --> Customize --> Communities --> Communities Settings`, you will find the section called Sharing Sets. 
Create a new sharing set with name and add the relevant profiles. 

1. Add Access to Contact Then add Account to your available objects list. 
2. Then, below select object, you can find the section to configure access to these objects. 
3. Click on Setup besides Accounts. 
4. Set the rule as shown below.
Grant access where User Contact.Account, Matches Target Account Id, Access Level Read/Write

:End draft

## Permission Sets

| Name                           |          Description           |
|:-------------------------------|:------------------------------:|
| CM Admin Permissions           | Admins permissions for package |
| CM Advisor Permissions         |                                |
| CM Lead Advisor Permissions    |                                |
| CM Member Permissions          |                                |
| CM Technical Chair Permissions |                                |

## Manual Steps

Enable custom address field
Create Sharing Rules as following

![sharingRule1.png](../../../img/conference-management/sharingRule1.png)
![sharingRule2.png](../../../img/conference-management/sharingRule2.png)
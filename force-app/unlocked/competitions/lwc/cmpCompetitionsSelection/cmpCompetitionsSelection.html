<template>
    <div class="slds-is-relative">
        <lightning-spinner lwc:if={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>
        <template lwc:if={showAllowDelegatesCheckbox}>
            <lightning-input
                lwc:ref="delegateCheckbox"
                variant="label-stacked"
                class="slds-m-vertical_small"
                type="checkbox"
                label="Mark as Delegate">
            </lightning-input>
        </template>
        <template lwc:if={allowMultipleCompetitions}>
            <lightning-dual-listbox
                lwc:ref="select"
                label={labels.selectCompetitionLabel}
                variant="label-stacked"
                source-label={labels.competitionAvailableLabel}
                selected-label={labels.competitionSelectedLabel}
                options={options}
                value={values}
                onchange={handleChange}
                field-level-help="If you select more than one competition to participate in, please rank them in the Selected Competition section from highest to lowest preference. This will assist us in assigning you to the appropriate competition if you qualify for multiple."
                required
            ></lightning-dual-listbox>
            <p class="slds-p-horizontal_xx-small">
                <lightning-input
                    lwc:ref="confirmCheckbox"
                    variant="label-stacked"
                    type="checkbox"
                    required
                    label="By checking this box, you confirm that you have ranked your selected competitions in order of preference and understand that this will determine your placement if you qualify for multiple competitions"
                ></lightning-input>
            </p>
        </template>
        <template lwc:else>
            <lightning-select
                lwc:ref="select"
                label={labels.selectCompetitionLabel}
                variant="label-stacked"
                value={value}
                options={options}
                onchange={handleChange}
                required
            ></lightning-select>
        </template>

        <div lwc:if={hasInfoAlert} class="info-alert slds-p-around_small slds-m-top_small">
            <div lwc:if={hasExcludedByLimitCompetitions}>
                Please note, that you have reached the limit for your school for the following competitions:
                <ul class="slds-list_dotted">
                    <li for:each={excludedByLimitCompetitions} for:item="item" key={item.competition.Id}>
                        <b>{item.competition.Name} <small>[Limit: {item.limitOfCompetitors} competitors]</small></b>
                    </li>
                </ul>
            </div>
            <div lwc:if={hasExcludedFreshmanCompetitions}>
                Some competitions are excluded as they designed for freshman only:
                <ul class="slds-list_dotted">
                    <li for:each={freshmanOnlyCompetitions} for:item="item" key={item.competition.Id}>
                        <b>{item.competition.Name}</b>
                    </li>
                </ul>
            </div>
        </div>

        <div class="slds-m-bottom_medium"></div>

    </div>
</template>

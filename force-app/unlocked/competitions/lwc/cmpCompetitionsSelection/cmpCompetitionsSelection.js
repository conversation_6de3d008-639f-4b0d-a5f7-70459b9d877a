import { api, LightningElement } from "lwc";
import { showErrors } from "c/errorHandler";
import getCompetitions from "@salesforce/apex/cmp_CompetitionsSelectionCtrl.getCompetitions";
import labels from "./labels";

export default class extends LightningElement {
    @api contactId;
    @api conferenceId;
    @api isDelegatesLimitReached = false;
    allowMultipleCompetitions = false;
    labels = labels;
    isLoading = true;
    options = [];
    values = [];
    orderedValues = [];
    value;
    useLimits = false;
    excludedByLimitCompetitions = [];
    freshmanOnlyCompetitions = [];
    competitionsWithAllowDelegateIds = [];
    teamCompetitionIds = [];

    @api
    getValues() {
        return this.values;
    }

    @api
    getOrderedValues() {
        return this.orderedValues;
    }

    @api
    getDelegateValue() {
        return this.showAllowDelegatesCheckbox && this.refs.delegateCheckbox.checked;
    }

    @api
    hasTeamCompetitions() {
        if (this.allowMultipleCompetitions) {
            return this.values.some(value => this.teamCompetitionIds.includes(value));
        }
        return this.teamCompetitionIds.includes(this.values);
    }

    @api
    reportValidity() {
        if (this.allowMultipleCompetitions === true) {
            return this.refs.confirmCheckbox.reportValidity();
        }

        return this.refs.select.reportValidity();
    }

    get hasInfoAlert() {
        return this.useLimits && this.hasExcludedByLimitCompetitions || this.hasExcludedFreshmanCompetitions;
    }

    get hasExcludedByLimitCompetitions() {
        return this.useLimits && this.excludedByLimitCompetitions.length > 0;
    }

    get hasExcludedFreshmanCompetitions() {
        return this.freshmanOnlyCompetitions.length > 0;
    }

    get showAllowDelegatesCheckbox() {
        return !this.isDelegatesLimitReached && !this.allowMultipleCompetitions && this.competitionsWithAllowDelegateIds?.includes(this.values);
    }

    connectedCallback() {
        getCompetitions({ conferenceId: this.conferenceId, contactId: this.contactId })
            .then(result => {

                const isFreshman = result.competitor?.IsFreshman__c;
                this.allowMultipleCompetitions = result.allowMultipleParticipation;
                this.useLimits = result.useLimits;

                this.excludedByLimitCompetitions = result.allCompetitions.filter(item => item.remainingCompetitors <= 0);
                this.freshmanOnlyCompetitions = result.allCompetitions.filter(item => !isFreshman && item.competition.Subtype__c === 'FRESHMAN ONLY');
                this.competitionsWithAllowDelegateIds = result.allCompetitions
                    .filter(item => item.competition.AllowDelegate__c === true)
                    .map(item => item.competition.Id);
                this.teamCompetitionIds = result.allCompetitions
                    .filter(item => item.competition.IsTeamCompetition__c === true)
                    .map(item => item.competition.Id);

                const selectedValues = result.selectedCompetitionOrders
                    ?.sort((a, b) => a.order - b.order)
                    ?.map(item => item.competitionId);
                if (this.allowMultipleCompetitions) {
                    this.values = selectedValues;
                }
                else {
                    this.value = selectedValues?.[0];
                }

                let options = result.allCompetitions;
                if (this.useLimits) {
                    // Do not include competitions where limit is reached
                    options = options.filter(item => {
                        return item.remainingCompetitors > 0
                            || this.values.includes(item.competition.Id)
                            || this.value === item.competition.Id;
                    });
                }
                // Do not include competitions designed for Freshman only for non freshmen
                options = options.filter(item => isFreshman || !isFreshman && item.competition.Subtype__c !== 'FRESHMAN ONLY');

                options = options.map(item => {
                    let label = this.getOptionLabel(item);
                    let selectedItem = result.selectedCompetitionOrders?.find(el => el.competitionId === item.competition.Id);
                    let isSelected = !!selectedItem;
                    let indexValue = isSelected && this.allowMultipleCompetitions === true ? `[${selectedItem.order}] ` : '';

                    return {
                        originalLabel: label,
                        index: isSelected ? selectedItem.order : 0,
                        label: indexValue + label,
                        value: item.competition.Id
                    }
                });

                if (!this.allowMultipleCompetitions) { // Force user to select specific option in case of single selector enabled
                    options.unshift({
                        label: '---',
                        value: ''
                    });
                }

                this.options = options;
            })
            .catch((error) => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            })
    }

    getOptionLabel(cmp) {
        const competition = cmp.competition;
        let teamOrIndividual = competition.IsTeamCompetition__c ? '(Team)' : '(Individual)';
        let hasSubtype = !!competition.Subtype__c;
        let hasType = !!competition.Type__c;
        let type = '';

        if (hasSubtype) {
            type = `(Type: ${competition.Type__c} / Subtype: ${competition.Subtype__c})`;
        }
        else if (hasType) {
            type = `(Type: ${competition.Type__c})`;
        }

        return `${competition.Name} ${type}${teamOrIndividual}`
    }

    handleChange(event) {
        this.values = event.detail?.value;

        this.options = this.options.map(item => {
            const isSelected = this.values.includes(item.value);
            let index = this.values.indexOf(item.value);

            item.label = `${isSelected && this.allowMultipleCompetitions ? `[${index + 1}]` : ''} ${item.originalLabel}`;
            item.index = index + 1;
            return item;
        });

        this.orderedValues = this.options
            .filter(item => item.value && item.index !== undefined && item.index > 0)
            .sort((a, b) => a.index - b.index)
            .map(item => ({
                competitionId: item.value,
                order: item.index
            }));
    }

}

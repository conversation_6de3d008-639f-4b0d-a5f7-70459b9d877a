import { api, LightningElement } from "lwc";
import getCompetitionDetails from '@salesforce/apex/cmp_OverviewCtrl.getCompetitionDetails'
import { showErrors } from 'c/errorHandler'

const columns = [
    { label: 'School', fieldName: 'school', sortable: true },
    { label: 'Division', fieldName: 'division', sortable: true },
    { label: 'First Name', fieldName: 'firstname', sortable: true },
    { label: 'Last Name', fieldName: 'lastname', sortable: true },
    { label: 'Contestant Number', fieldName: 'contestantNumber', sortable: true },
    { label: 'Disqualified', fieldName: 'disqualified', sortable: true },
];

export default class extends LightningElement {
    @api recordId;
    showCompetitors = false;
    competitors;
    competitorsSize = 0;
    columns = columns;
    defaultSortDirection = 'asc';
    sortDirection = 'asc';
    sortedBy;

    connectedCallback() {
        getCompetitionDetails({ recordId: this.recordId })
            .then(result => {
                this.showCompetitors = result.showDetails === true;
                this.competitors = result.competition.CompetitionParticipants__r?.map(el => {
                    return {
                        id: el?.Id,
                        school: el.Participant__r?.Account?.Name,
                        division: el.Participant__r?.Division__c,
                        firstname: el.Participant__r?.FirstName,
                        lastname: el.Participant__r?.LastName,
                        team: el.TeamId__c,
                        contestantNumber: el.ContestantNumberFormula__c,
                        disqualified: el.IsDisqualified__c === true ? 'yes' : '',
                    }
                });
                this.competitorsSize = this.competitors?.length;

                if (result.IsTeamCompetition__c === true) {
                    this.columns.push({ label: 'Team', fieldName: 'team', sortable: true })
                }

            })
            .catch(error => {
                showErrors(this, error);
            })
    }

    sortBy(field, reverse, primer) {
        const key = primer
            ? function (x) {
                return primer(x[field]);
            }
            : function (x) {
                return x[field];
            };

        return function (a, b) {
            a = key(a);
            b = key(b);
            return reverse * ((a > b) - (b > a));
        };
    }

    onHandleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const cloneData = [...this.competitors];

        cloneData.sort(this.sortBy(sortedBy, sortDirection === 'asc' ? 1 : -1));
        cloneData.sort();
        this.competitors = cloneData;
        this.sortDirection = sortDirection;
        this.sortedBy = sortedBy;
    }

}
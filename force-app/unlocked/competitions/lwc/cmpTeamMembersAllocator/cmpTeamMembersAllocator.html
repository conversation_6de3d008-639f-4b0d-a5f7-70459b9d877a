<template>
    <div class="slds-is-relative">
        <lightning-spinner if:true={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>
        <h2 class="slds-text-heading_medium slds-m-bottom_xx-small">Competitors allocator for "{ competitionName }"</h2>
        <h3 class="slds-text-title slds-m-bottom_small">Allocate competitors to the specific teams and roles.</h3>

        <hr>


<!--        <template lwc:if={hasMultipleTeams}>-->
            <template for:each={teams} for:item="team">
                <div key={team.label} class="slds-m-bottom_medium">
                    <lightning-card icon-name="standard:groups" title={team.label}>
                        <hr class="slds-m-vertical_none"/>
                        <div class="slds-grid slds-gutters">
                            <template for:each={options} for:item="role">
                                <div key={role.label} class="slds-col">
                                    <lightning-card icon-name="standard:user_role" title={role.label}>
                                        <div class="slds-p-horizontal_medium">
                                            <lightning-select
                                                data-team={team.value}
                                                data-role={role.value}
                                                variant="label-hidden"
                                                disabled={isFormDisabled}
                                                options={participantOptions}
                                                onchange={handleParticipantSelection}
                                            ></lightning-select>
                                        </div>
                                    </lightning-card>
                                </div>
                            </template>
                        </div>
                    </lightning-card>
                </div>
            </template>
<!--        </template>-->

        <div lwc:if={showSaveButton} class="slds-clearfix">
            <lightning-button onclick={handleSave} disabled={isFormDisabled} label="Save" variant="destructive-text" class="slds-float_right"></lightning-button>
        </div>




<!--        <table class="slds-table slds-table_cell-buffer slds-table_bordered" aria-labelledby="element-with-table-label other-element-with-table-label">-->
<!--            <thead>-->
<!--            <tr class="slds-line-height_reset">-->
<!--                <th scope="col">-->
<!--                    <div>Competitor Name</div>-->
<!--                </th>-->
<!--                <th lwc:if={hasMultipleTeams} scope="col">-->
<!--                    <div>Team</div>-->
<!--                </th>-->
<!--                <th scope="col">-->
<!--                    <div>Team Role</div>-->
<!--                </th>-->
<!--            </tr>-->
<!--            </thead>-->
<!--            <tbody>-->
<!--            <tr for:each={competitors} for:item="competitor" key={competitor.id} class="slds-hint-parent">-->
<!--                <th scope="row">-->
<!--                    <div class="slds-truncate">-->
<!--                        {competitor.name}-->
<!--                    </div>-->
<!--                </th>-->
<!--                <td lwc:if={hasMultipleTeams}>-->
<!--                    <div>-->
<!--                        <lightning-select-->
<!--                            data-name="teams"-->
<!--                            data-competitor-id={competitor.id}-->
<!--                            disabled={isFormDisabled}-->
<!--                            variant="label-hidden"-->
<!--                            options={teams}-->
<!--                            value={competitor.team}-->
<!--                            onchange={handleTeamChange}-->
<!--                        ></lightning-select>-->
<!--                    </div>-->
<!--                </td>-->
<!--                <td>-->
<!--                    <div>-->
<!--                        <lightning-select-->
<!--                            data-name="roles"-->
<!--                            data-competitor-id={competitor.id}-->
<!--                            disabled={isFormDisabled}-->
<!--                            variant="label-hidden"-->
<!--                            options={options}-->
<!--                            value={competitor.role}-->
<!--                            onchange={handleRoleChange}-->
<!--                        ></lightning-select>-->
<!--                    </div>-->
<!--                </td>-->
<!--            </tr>-->
<!--            </tbody>-->
<!--        </table>-->



    </div>
</template>
import { LightningElement, api } from "lwc";
import { showErrors } from "c/errorHandler";
import init from "@salesforce/apex/cmp_TeamMemberAllocatorCtrl.init"
import save from "@salesforce/apex/cmp_TeamMemberAllocatorCtrl.save"
import { ShowToastEvent } from "lightning/platformShowToastEvent";

export default class extends LightningElement {
    @api competitionId;
    participants = [];
    isLoading = true;
    options = [];
    numberOfTeams = 1;
    teams = [];
    competitionName;
    participantsLength = 0;
    competitorsPerTeam = 0;
    competitors = [];
    isFormDisabled = true;

    participantOptions = [];

    connectedCallback() {
        init({competitionId: this.competitionId})
            .then(result => {
                console.log('result', result);

                this.competitionName = result.competition.Name;
                this.participants = result.participants;
                this.participantsLength = result.participants.length;
                this.numberOfTeams = result.numberOfTeams;
                this.competitorsPerTeam = result.competitorsPerTeam;
                this.isFormDisabled = result.availableForEdit !== true;

                this.participantOptions = result.participants.map(el => {
                    return {
                        label: el.Participant__r.Name,
                        value: el.Id,
                        disabled: false,
                        team: el.TeamId__c,
                        role: el.Role__c,
                    }
                });

                this.participantOptions.unshift({
                    label: '---',
                    value: '---',
                    disabled: false
                });

                this.competitors = result.participants.map(el => {
                   return {
                       id: el.Id,
                       name: el.Participant__r.Name,
                       team: parseInt(el.TeamId__c) || null,
                       role: el.Role__c,
                   }
                });

                // if (this.numberOfTeams > 1) {
                    let teams = Array(this.numberOfTeams).keys().toArray().map(el => ({
                        label: "Team " + (el + 1),
                        value: el + 1,
                        disabled: this.competitors.filter(item => parseInt(item.team) === el + 1).length >= this.competitorsPerTeam
                    }));
                    this.teams = teams;
                // }


                let options = result.availableRoles.map(el => ({
                    value: el,
                    label: "Role " + el,
                    disabled: false,
                }));


                this.options = options;
                this.showSaveButton = true;
            })
            .then(() => {
                this.template.querySelectorAll('lightning-select[data-role]').forEach(el => {
                    let team = el.getAttribute('data-team');
                    let role = el.getAttribute('data-role');

                    let foundEl = this.participantOptions.find(item => item.team === team && item.role === role);
                    if (foundEl) {
                        el.value = foundEl.value;
                    }
                });

                this.handleParticipantSelection();
            })
            .catch(error => {
                showErrors(this, error)
            })
            .finally(() => {
                this.isLoading = false;
            })
    }

    get hasMultipleTeams() {
        return this.numberOfTeams > 1;
    }

    get selectedAllTeams() {
        return this.teams.every(el => el.disabled);
    }

    handleParticipantSelection() {
        let optionsToDisable = [];
        this.template.querySelectorAll('lightning-select[data-role]').forEach(el => {
            optionsToDisable.push(el.value);
        });

        this.participantOptions = this.participantOptions.map(el => {
            return {
                label: el.label,
                value: el.value,
                disabled: el.value !== '---' && optionsToDisable.includes(el.value),
            }
        });
    }

    // handleRoleChange(ev) {
    //     let competitorId = ev.target.getAttribute('data-competitor-id');
    //     let value = ev.target.value;
    //
    //     this.competitors = this.competitors.map(el => {
    //         if (el.id === competitorId) {
    //             el.role = value
    //         }
    //         return el;
    //     });
    // }
    //
    // handleTeamChange(ev) {
    //     let competitorId = ev.target.getAttribute('data-competitor-id');
    //     let value = parseInt(ev.target.value) || null;
    //
    //     this.competitors = this.competitors.map(el => {
    //         if (el.id === competitorId) {
    //             el.team = value
    //         }
    //         return el;
    //     });
    //
    //     this.teams = this.teams.map(el => {
    //         el.disabled = this.competitors.filter(item => item.team === el.value).length >= this.competitorsPerTeam;
    //         return el;
    //     })
    // }

    handleSave() {

        let toSave = [];

        this.template.querySelectorAll('lightning-select[data-role]').forEach(el => {
            if (el.value && el.value !== '---') {
                toSave.push({
                    id: el.value,
                    name: undefined,
                    team: el.getAttribute('data-team'),
                    role: el.getAttribute('data-role'),
                });
            }
        });

        console.log('toSave', JSON.stringify(toSave));


        save({competitionId: this.competitionId, jsonMap: JSON.stringify(toSave)})
            .then(() => {
                const event = new ShowToastEvent({
                    title: 'Success!',
                    message: 'Records saved.',
                    variant: 'success'
                });
                this.dispatchEvent(event);
            })
            .catch(error => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            })

        return;

        let teamInputs = this.template.querySelectorAll('[data-name="teams"]');
        let roleInputs = this.template.querySelectorAll('[data-name="roles"]');

        let teamInputsValid = [];
        let roleInputsValid = [];

        teamInputs.forEach(el => {
            let isValid = el.reportValidity();
            teamInputsValid.push(isValid);
        });
        roleInputs.forEach(el => {
            let isValid = el.reportValidity();
            roleInputsValid.push(isValid);
        });

        console.log('teamInputs', teamInputs);
        console.log('roleInputs', roleInputs);


        let isTeamInputsValid = teamInputsValid.every(el => el);
        let isRoleInputsValid = roleInputsValid.every(el => el);

        const groupedTeams = this.competitors.reduce((acc, item) => {
            // Find the team in the accumulator array
            let team = acc.find(t => t.team === item.team);

            if (!team) {
                // If the team does not exist, create a new team object
                team = { team: item.team, roles: [] };
                acc.push(team);
            }

            // Add the role to the roles array if it doesn't already exist
            if (!team.roles.includes(item.role)) {
                team.roles.push(item.role);
            }

            return acc;
        }, []);


        let teamsWithErrors = [];
        const checkTeams = (teams) => {

            for (const team of teams) {
                const allRoles = new Set();
                // Check if each team has exactly 2 roles
                if (team.roles.length !== this.competitorsPerTeam) {
                    teamsWithErrors.push(parseInt(team.team));
                    return false;
                }

                for (const role of team.roles) {
                    // Check if the role already exists in the set
                    if (allRoles.has(role)) {
                        teamsWithErrors.push(parseInt(team.team));
                        return false;
                    }
                    // Add the role to the set
                    allRoles.add(role);
                }
            }

            return true;
        };

        let areRolesCorrect = checkTeams(groupedTeams);

        console.log(isTeamInputsValid, isRoleInputsValid);

        if (isTeamInputsValid && isRoleInputsValid && areRolesCorrect) {
            this.isLoading = true;
            save({jsonMap: JSON.stringify(this.competitors)})
                .then(() => {
                    const event = new ShowToastEvent({
                        title: 'Success!',
                        message: 'Records saved.',
                        variant: 'success'
                    });
                    this.dispatchEvent(event);
                })
                .catch(error => {
                    showErrors(this, error);
                })
                .finally(() => {
                    this.isLoading = false;
                })
        }
        else if (!areRolesCorrect) {
            // show validation error
            showErrors(this, 'Please review the form, each team must have unique roles.');
            this.template.querySelectorAll('[data-name="teams"]').forEach(el => {
                el.reportValidity();
                if (teamsWithErrors.includes(el.value)) {
                    el.setCustomValidity('Please choose unique role for this team.');
                    el.reportValidity();
                    el.setCustomValidity();
                }
                console.log('> ', el.value);
            })
        }
        else {
            // show validation error
            showErrors(this, 'Please review the form');
        }


    }
}
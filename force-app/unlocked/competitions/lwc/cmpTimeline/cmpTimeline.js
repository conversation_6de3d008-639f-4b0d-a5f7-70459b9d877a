import { LightningElement, api } from 'lwc';
import { showErrors } from 'c/errorHandler';
import getCompetitionDetails from '@salesforce/apex/cmp_TimelineCtrl.getCompetitionDetails';
import getTimelinesByCompetitionId from '@salesforce/apex/cmp_TimelineCtrl.getTimelinesByCompetitionId';
import UPDATE_PRIZE_INFO_MODAL from './modal';
import LightningAlert from 'lightning/alert';
import labels from './labels';

const CLASS_LIST = 'slds-timeline__item_expandable slds-is-open slds-p-bottom_none';
const FILE_URL = '/sfc/servlet.shepherd/document/download/';

export default class extends LightningElement {
    @api recordId;
    isReadOnly = true;
    timelines;
    labels = labels;

    // Prize Info Action
    isPrizeInfoSubmitted = false;
    isPrizeButtonDisabled = false;
    prizeInfoTimelineId = '';

    // File Upload accepted formats
    get acceptedFormats() {
        return ['.pdf', '.png', '.jpg', '.jpeg', '.xls', '.xlsx', '.csv', '.doc', '.docx'];
    }

    handleUploadFinished() {
        LightningAlert.open({
            theme: 'success',
            label: 'The file successfully uploaded', // this is the header text
        });
    }

    connectedCallback() {
        getCompetitionDetails({ recordId: this.recordId })
            .then((competition) => {
                this.isReadOnly = competition.showDetails === false;
                return getTimelinesByCompetitionId({ recordId: this.recordId })
                    .then((result) => {
                        this.timelines =
                            result.length === 0
                                ? null
                                : result.map((el) => {
                                    let isOpen = new Date() <= new Date(el.Deadline__c + 'T23:59:59');
                                    let actions = [];
                                    if (el.CompetitionTimelineActions__r) {
                                        actions = el.CompetitionTimelineActions__r.map((action) => {
                                            let isPrizeInformation = action.Type__c === 'Prize Information';
                                            if (isPrizeInformation) {
                                                this.isPrizeInfoSubmitted = el.PrizeInfoStatus__c === 'Will Be Provided';
                                                this.isPrizeButtonDisabled = this.isReadOnly || !isOpen ||
                                                    (el.PrizeInfoStatus__c === 'Will Be Provided' ||
                                                        el.PrizeInfoStatus__c === 'Will NOT be Provided');
                                                this.prizeInfoTimelineId = el.Id;
                                            }
                                            let fileUrl;
                                            if (el.AttachedContentDocuments && el.AttachedContentDocuments.length > 0) {
                                                fileUrl = FILE_URL + el.AttachedContentDocuments[0]?.ContentDocumentId;
                                            }

                                            return {
                                                actionId: action.Id,
                                                timelineId: el.Id,
                                                isButton: action.Type__c === 'Button',
                                                isText: action.Type__c === 'Text Section',
                                                isPrizeInformation: isPrizeInformation,
                                                isUploadFile: action.Type__c === 'Upload File',
                                                text: action.Text__c,
                                                buttonVariant: action.ButtonStyle__c,
                                                buttonLink: action.ButtonLink__c,
                                                theLastFileUrl: fileUrl,
                                                isReadOnly: this.isReadOnly || !isOpen
                                            };
                                        });
                                    }

                                    let iconName = el.IconName__c ?? 'task';
                                    return {
                                        name: el.Name,
                                        description: el.Description__c,
                                        deadline: el.Deadline__c,
                                        icon: 'standard:' + iconName,
                                        className: CLASS_LIST + ' slds-timeline__item_' + iconName,
                                        isOpen: isOpen,
                                        actions: actions
                                    };
                                });
                    })

            })
            .catch((error) => {
                showErrors(this, error);
            });
    }

    async updatePrizeInfo() {
        await UPDATE_PRIZE_INFO_MODAL.open({
            size: 'small',
            timelineId: this.prizeInfoTimelineId
        }).then((result) => {
            if (result === 'submitted') {
                this.isPrizeButtonDisabled = true;
                this.isPrizeInfoSubmitted = true;
            }
        });
    }
}

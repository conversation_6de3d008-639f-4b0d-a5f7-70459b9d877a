import LightningModal from 'lightning/modal';
import { api } from 'lwc';
import savePrizeInfo from "@salesforce/apex/cmp_TimelineCtrl.savePrizeInfo";
import { showErrors } from "c/errorHandler";
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import labels from "./labels";

export default class extends LightningModal {
    @api timelineId;
    labels = labels;
    showForm = false;

    handlePrizeInfoStatusChange(ev) {
        let value = ev.detail.value;
        this.showForm = value === 'Will Be Provided';
    }

    save() {
        let params = {
            timelineId: this.timelineId,
            status: this.template.querySelector("lightning-input-field[data-name='PrizeInfoStatus__c']")?.value,
            goldPrize: this.template.querySelector("lightning-input-field[data-name='PrizesForGold__c']")?.value,
            silverPrize: this.template.querySelector("lightning-input-field[data-name='PrizesForSilver__c']")?.value,
            bronzePrize: this.template.querySelector("lightning-input-field[data-name='PrizesForBronze__c']")?.value,
        };

        savePrizeInfo(params)
            .then(() => {
                const event = new ShowToastEvent({
                    title: 'Success',
                    message: 'Operation completed',
                    variant: 'success'
                });
                this.dispatchEvent(event);
                this.close('submitted');
            })
            .catch(errors => showErrors(this, errors))
    }
}
import timelineLabel from '@salesforce/label/c.cmpCompetitionTimelineLabel';
import openDeadlineLabel from '@salesforce/label/c.cmpCompetitionTimelineOpenDeadlineLabel';
import passedDeadlineLabel from '@salesforce/label/c.cmpCompetitionTimelinePassedDeadlineLabel';
import cmCompetitionDetailsTimelinePrizeInfoNotSubmitted from '@salesforce/label/c.cmpCompetitionDetailsTimelinePrizeInfoNotSubmitted';
import cmCompetitionDetailsTimelinePrizeInfoSubmitted from '@salesforce/label/c.cmpCompetitionDetailsTimelinePrizeInfoSubmitted';
import cmCompetitionNoRecordsFound from '@salesforce/label/c.cmpCompetitionNoRecordsFound';
import cmUpdateDescriptionModalHeader from '@salesforce/label/c.cmpUpdateDescriptionModalHeader';
import cmUpdateDescriptionConfirmMessage from '@salesforce/label/c.cmpUpdateDescriptionConfirmMessage';
import cmCompetitionDetailsTimelinePrizeInfoStatusFieldLabel from '@salesforce/label/c.cmpCompetitionDetailsTimelinePrizeInfoStatusFieldLabel';
import cmCompetitionDetailsTimelineSaveButtonText from '@salesforce/label/c.cmpCompetitionDetailsTimelineSaveButtonText';

export default {
    timelineLabel,
    openDeadlineLabel,
    passedDeadlineLabel,
    cmCompetitionDetailsTimelinePrizeInfoNotSubmitted,
    cmCompetitionDetailsTimelinePrizeInfoSubmitted,
    cmCompetitionNoRecordsFound,
    cmUpdateDescriptionModalHeader,
    cmUpdateDescriptionConfirmMessage,
    cmCompetitionDetailsTimelinePrizeInfoStatusFieldLabel,
    cmCompetitionDetailsTimelineSaveButtonText,
};

<template>
    <lightning-modal-header label={modalLabel}></lightning-modal-header>
    <lightning-modal-body>
        <lightning-input
            type="text"
            label={labels.cmChairpersonEmailSubjectLabel}
            value={subject}
            required="true"
            onchange={changeSubject}
        ></lightning-input>
        <lightning-textarea
            label={labels.cmChairpersonEmailMessageLabel}
            value={emailMessage}
            required="true"
            onchange={changeEmailMessage}
        ></lightning-textarea>
    </lightning-modal-body>
    <lightning-modal-footer>
        <lightning-button
            label={labels.cmChairpersonSendEmailButtonLabel}
            variant="brand"
            onclick={sendEmail}
            disabled={disableSendEmail}
        ></lightning-button>
    </lightning-modal-footer>
</template>

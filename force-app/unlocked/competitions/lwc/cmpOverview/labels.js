import headerLabel from '@salesforce/label/c.cmpCompetitionOverviewHeaderLabel';
import clusterLabel from '@salesforce/label/c.cmpCompetitionOverviewClusterLabel';
import descriptionLabel from '@salesforce/label/c.cmpCompetitionOverviewDescriptionLabel';
import updateDescriptionButtonLabel from '@salesforce/label/c.cmpCompetitionOverviewUpdateDescriptionButtonLabel';
import technicalStandardsLabel from '@salesforce/label/c.cmpCompetitionOverviewTechnicalStandardsLabel';
import technicalStandardsButtonLabel from '@salesforce/label/c.cmpCompetitionOverviewTechnicalStandardsButtonLabel';
import logisticHeader from '@salesforce/label/c.cmpCompetitionOverviewLogisticHeader';
import buildingPlanLabel from '@salesforce/label/c.cmpCompetitionOverviewBuildingPlanLabel';
import downloadPlanLabel from '@salesforce/label/c.cmpCompetitionOverviewDownloadPlanLabel';
import locationLabel from '@salesforce/label/c.cmpCompetitionOverviewLocationLabel';
import timeLabel from '@salesforce/label/c.cmpCompetitionOverviewTimeLabel';
import briefingLocationLabel from '@salesforce/label/c.cmpCompetitionOverviewBriefingLocationLabel';
import briefingTimeLabel from '@salesforce/label/c.cmpCompetitionOverviewBriefingTimeLabel';
import cmUpdateDescriptionModalHeader from '@salesforce/label/c.cmpUpdateDescriptionModalHeader';
import cmUpdateDescriptionConfirmMessage from '@salesforce/label/c.cmpUpdateDescriptionConfirmMessage';

export default {
    headerLabel,
    clusterLabel,
    descriptionLabel,
    updateDescriptionButtonLabel,
    technicalStandardsLabel,
    technicalStandardsButtonLabel,
    logisticHeader,
    buildingPlanLabel,
    downloadPlanLabel,
    locationLabel,
    timeLabel,
    briefingLocationLabel,
    briefingTimeLabel,
    cmUpdateDescriptionModalHeader,
    cmUpdateDescriptionConfirmMessage
};

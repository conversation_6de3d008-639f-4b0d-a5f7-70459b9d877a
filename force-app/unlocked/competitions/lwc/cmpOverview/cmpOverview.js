import { LightningElement, api } from 'lwc';
import getCompetitionDetails from '@salesforce/apex/cmp_OverviewCtrl.getCompetitionDetails'
import canUpdateCompetitionDescription from '@salesforce/customPermission/cmp_CanUpdateCompetitionDescription';
import UPDATE_DESCRIPTION_MODAL from './modal';
import { showErrors } from "c/errorHandler";
import labels from './labels';

export default class extends LightningElement {
    @api recordId;
    isReadOnly = true;
    labels = labels;
    overviewInfo;
    canUpdateCompetitionDescription = canUpdateCompetitionDescription;

    connectedCallback() {
        getCompetitionDetails({recordId: this.recordId})
            .then((result) => {
                const cmp = result.competition;
                this.isReadOnly = result.showDetails === false;
                this.overviewInfo = {
                    cluster: cmp.CompetitionCluster__c,
                    description: cmp.CompetitionDescription__c,
                    technicalStandardsDescription: cmp.TechnicalStandardsDescription__c,
                    technicalStandardsLink: cmp.TechnicalStandardsDescriptionLink__c,
                    competitionLocation: {
                        address: cmp.CompetitionLocationAddress__c,
                        latitude: cmp.CompetitionLocation__Latitude__s,
                        longitude: cmp.CompetitionLocation__Longitude__s
                    },
                    competitionTime: cmp.CompetitionTime__c,
                    briefingLocation: {
                        address: cmp.BriefingLocationAddress__c,
                        latitude: cmp.BriefingLocation__Latitude__s,
                        longitude: cmp.BriefingLocation__Longitude__s
                    },
                    briefingTime: cmp.BriefingTime__c,
                    buildingPlanLink: cmp.BuildingPlanLink__c
                }
            })
            .catch(error => {
                showErrors(this, error)
            });
    }

    get disableTechnicalStandardsButton() {
        return !this.overviewInfo.technicalStandardsLink;
    }

    openExternalLink() {
        window.open(this.overviewInfo.technicalStandardsLink, '_blank');
    }

    openMap(event) {
        const latitude = event.currentTarget.dataset.latitude;
        const longitude = event.currentTarget.dataset.longitude;
        if (latitude && longitude) {
            const mapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}`;
            window.open(mapsUrl, '_blank');
        }
    }

    async updateDescription() {
        await UPDATE_DESCRIPTION_MODAL.open({
            size: 'small',
            competitionId: this.recordId
        });
    }
}

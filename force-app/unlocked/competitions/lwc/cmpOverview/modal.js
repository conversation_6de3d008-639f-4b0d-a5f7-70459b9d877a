import { api } from 'lwc';
import LightningModal from 'lightning/modal';
import DESCRIP<PERSON>ON from '@salesforce/schema/cmp_Description__c.Description__c';
import NOTES from '@salesforce/schema/cmp_Description__c.NotesForApprover__c';
import LightningConfirm from "lightning/confirm";
import labels from './labels';

export default class extends LightningModal {
    @api competitionId;
    fields = [DESCRIPTION, NOTES];
    labels = labels;

    async onSubmit(event){
        event.preventDefault(); // stop the form from submitting
        const isConfirmed = await LightningConfirm.open({
            label: this.labels.cmUpdateDescriptionConfirmMessage
        });

        if (isConfirmed) {
            const fields = event.detail.fields;
            fields.Competition__c = this.competitionId; // modify a field
            this.template.querySelector('lightning-record-form').submit(fields);
        }
    }
}

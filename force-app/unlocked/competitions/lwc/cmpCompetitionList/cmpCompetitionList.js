import { LightningElement, api } from "lwc";
import { showErrors } from 'c/errorHandler';
import getAvailableCompetitions from "@salesforce/apex/cmp_CompetitionListCtrl.getAvailableCompetitions"

const columns = [
    {
        label: 'Name',
        fieldName: 'nameUrl',
        type: 'url',
        typeAttributes: {
            label: { fieldName: 'Name' },
            target: '_blank'
        }
    },
    {
        label: 'Division',
        fieldName: 'Division__c',
    },
    {
        label: 'Type',
        fieldName: 'Type__c',
    },
    {
        label: 'Is Team Competition?',
        fieldName: 'IsTeamCompetition__c',
    },
    {
        label: 'Competition Time',
        fieldName: 'CompetitionTime__c',
    }
];

export default class extends LightningElement {
    @api conferenceId;
    @api role;
    @api url = '/s/cmp-competition/{competitionId}';

    isLoading = true;
    data = [];
    columns = columns;
    hasCompetitions = false;

    connectedCallback() {
        getAvailableCompetitions({conferenceId: this.conferenceId, role: this.role})
            .then(response => {
                this.hasCompetitions = response?.length > 0;
                this.data = response.map(row => {
                    let nameUrl = this.url.replace('{competitionId}', row.Id);
                    let competitionTime = row.CompetitionTime__c;

                    if (!!competitionTime) {
                        competitionTime = new Date(row.CompetitionTime__c);
                        competitionTime = competitionTime.toLocaleString("en-US", {
                            month: "numeric",
                            day: "numeric",
                            year: "numeric",
                            hour: "numeric",
                            minute: "numeric",
                            hour12: true
                        });
                    }




                    row.IsTeamCompetition__c = row.IsTeamCompetition__c ? 'yes' : 'no';
                    row.CompetitionTime__c = competitionTime;
                    return {...row , nameUrl}
                })
            })
            .catch(error => {
                showErrors(this, error)
            })
            .finally(() => {
                this.isLoading = false;
            })
    }

}
<template>
    <lightning-modal-header label="Send Email to Judges"></lightning-modal-header>
    <lightning-modal-body>
        <div class="slds-is-relative">
            <lightning-spinner if:true={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>
            <lightning-input
                type="text"
                label="Subject"
                value={subject}
                required="true"
                onchange={changeSubject}
            ></lightning-input>
            <lightning-textarea
                label="Email Message"
                value={emailMessage}
                required="true"
                onchange={changeEmailMessage}
            ></lightning-textarea>
        </div>
    </lightning-modal-body>
    <lightning-modal-footer>
        <lightning-button
            label="Send Email"
            variant="brand"
            onclick={sendEmail}
            disabled={disableSendEmail}
        ></lightning-button>
    </lightning-modal-footer>
</template>
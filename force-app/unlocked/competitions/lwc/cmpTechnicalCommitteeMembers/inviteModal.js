import { api } from "lwc";
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import LightningModal from "lightning/modal";
import { showErrors } from "c/errorHandler";
import createJudgeInvite from "@salesforce/apex/cmp_TechnicalCommitteeMembersCtrl.createJudgeInvite"
import createJudgeEmailInvite from "@salesforce/apex/cmp_TechnicalCommitteeMembersCtrl.createJudgeEmailInvite"
import labels from "./labels";

export default class extends LightningModal {
    @api competition;
    labels = labels;
    isLoading = false;
    isInviteButtonDisabled = true;
    isInviteByEmailButtonDisabled = true;
    emailValue = '';
    judgeId;
    displayInfo = {
        primaryField: 'Name',
        additionalFields: ['Email', 'Account.Name', 'Phone'],
    };
    filter = {
        criteria: [
            {
                fieldPath: 'PortalRole__c',
                operator: 'includes',
                value: ['Partner Lead', 'Partner', 'Advisor', 'Lead Advisor']
            },
        ],
        filterLogic: '1 AND 2',
    };

    connectedCallback() {
        let criteria = {
            fieldPath: 'Id',
            operator: 'nin',
        };
        let contactIdsToExclude = [];
        if (this.competition?.CompetitionJudges__r) {
            contactIdsToExclude = this.competition.CompetitionJudges__r
                .filter(el => !!el.Contact__c) // skip element if Contact__c is null
                .map(el => el.Contact__c); // create map of ids, example: ['003..', '003...']
        }
        criteria.value = contactIdsToExclude;
        this.filter.criteria.push(criteria);
    }

    lookupChange(ev) {
        this.judgeId = ev.detail.recordId;
        this.isInviteButtonDisabled = !ev.detail.recordId;
    }

    emailChange(ev) {
        let element = this.template.querySelector('lightning-input');
        this.emailValue = ev.detail?.value;
        this.isInviteByEmailButtonDisabled = !!this.emailValue && !element.checkValidity();
    }

    handleInviteByEmailButtonClick() {
        this.isLoading = true;
        createJudgeEmailInvite({
            competitionId: this.competition.Id,
            email: this.emailValue
        })
            .then(() => {
                this.showSuccessToast();
                this.isInviteButtonDisabled = true;
            })
            .catch(error => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            })
    }

    handleInviteButtonClick() {
        this.isLoading = true;
        console.log('here', JSON.stringify(this.competition));
        createJudgeInvite({
            competitionId: this.competition.Id,
            judgeId: this.judgeId
        })
            .then(() => {
                this.showSuccessToast();
                this.isInviteButtonDisabled = true;
            })
            .catch(error => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            })
    }

    showSuccessToast() {
        this.dispatchEvent(
            new ShowToastEvent({
                title: 'Success',
                message: 'Operation complete',
                variant: 'success',
            }),
        );
    }
}
<template>
  <div class="slds-text-heading_medium slds-m-bottom_medium">{labels.cmCompetitionTechnicalCommitteeTitle}:</div>

  <div class="slds-grid slds-wrap slds-gutters">
    <div class="slds-col slds-medium-size_2-of-3 slds-small-size_1-of-1">
      <div class="slds-p-bottom_large">{labels.cmCompetitionTechnicalCommitteeDescription}</div>

      <template if:false={judges}>
        <div class="slds-text-align_center">
          <div class="slds-text-heading_medium slds-text-color_weak slds-p-vertical_xx-large">{labels.cmCompetitionNoRecordsFound}</div>
        </div>
      </template>

      <div class="slds-grid slds-nowrap slds-scrollable_x">
        <template for:each={judges} for:item="judge">
          <div class="person-card slds-m-right_medium" key={judge.contactId}>
            <lightning-card
                title={judge.name}
                icon-name="utility:user">
              <dl class="slds-p-horizontal_medium">
                <dt class="slds-text-color_weak">Email:</dt>
                <dd class="slds-m-bottom_x-small">{judge.email}</dd>
                <dt class="slds-text-color_weak">Associated with:</dt>
                <dd class="slds-m-bottom_x-small">{judge.accountName}</dd>
                <dt class="slds-text-color_weak">Status:</dt>
                <dd>{judge.approvalStatus}</dd>
              </dl>
            </lightning-card>
          </div>
        </template>
      </div>
    </div>
    <div class="slds-col slds-medium-size_1-of-3 slds-small-size_1-of-1">
      <div class="slds-m-bottom_medium">
        <lightning-button
            disabled={isReadOnly}
            stretch
            label="Send Email to Judges"
            variant="brand"
            class="slds-m-top_medium"
            onclick={sendEmailToJudges}
        ></lightning-button>
      </div>
      <div class="slds-m-bottom_medium">
        <span>{labels.cmCompetitionTechnicalCommitteeLinkDescription}:</span>
        <lightning-button
            disabled={isReadOnly}
            stretch
            label="Invite Judge"
            onclick={openModal}
        ></lightning-button>
      </div>
      <div lwc:if={bookHotelURL}>
        <template lwc:if={isJudge}>
          <span>Book a hotel:</span>
          <a href={bookHotelURL} target="_blank">
            <lightning-button
                stretch
                label="Book a hotel as Judge"
                class="slds-m-top_medium"
            ></lightning-button>
          </a>
        </template>
      </div>
    </div>
  </div>

</template>
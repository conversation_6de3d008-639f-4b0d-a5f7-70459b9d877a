import { api, LightningElement } from "lwc";
import labels from "./labels";
import { showErrors } from "c/errorHandler";
import INVITE_MODAL from './inviteModal';
import SEND_EMAIL_MODAL from './emailModal';
import getCompetitionDetails from '@salesforce/apex/cmp_TechnicalCommitteeMembersCtrl.getCompetitionDetails';
import getBookHotelURL from '@salesforce/apex/cmp_TechnicalCommitteeMembersCtrl.getBookHotelURL';
import isApprovedJudge from '@salesforce/apex/cmp_TechnicalCommitteeMembersCtrl.isApprovedJudge';

export default class extends LightningElement {
    @api recordId;
    isReadOnly = true;
    competition;
    isJudge = false;
    labels = labels;
    judges;
    bookHotelURL;

    connectedCallback() {
        console.log('>>>>', 1111);
        getCompetitionDetails({ recordId: this.recordId })
            .then(result => {

                console.log('>>>>', result);

                this.competition = result.competition;
                this.isReadOnly = result.showDetails === false;
                if(result.competition.CompetitionJudges__r) {
                    this.judges = result.competition.CompetitionJudges__r.map(el => {
                        return {
                            contactId: el.Contact__c,
                            name: el.Contact__r?.Name,
                            email: el.Contact__r?.Email || el.InvitedByEmail__c,
                            accountName: el.Contact__r?.Account.Name,
                            approvalStatus: el.ApprovalStatus__c
                        }
                    })
                }
            })
            .catch(error => {
                showErrors(this, error);
            });

        getBookHotelURL()
            .then(response => {
                if (response) {
                    this.bookHotelURL = response;
                }
            })
            .catch(error => {
                showErrors(this, error);
            });

        isApprovedJudge({recordId: this.recordId})
            .then(response => {
                this.isJudge = response;
            })
    }

    openModal() {
        INVITE_MODAL.open({
            size: 'small',
            competition: this.competition
        });
    }

    sendEmailToJudges() {
        SEND_EMAIL_MODAL.open({
            size: 'small',
            competitionId: this.competition.Id
        });
    }
}
<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Approved</name>
        <label>Approved</label>
        <locationX>314</locationX>
        <locationY>971</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>CustomNotificationRecord.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>RecipientIds</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <stringValue>Competition Description Update was updated</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <stringValue>Your suggestion was approved and submitted</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <name>NewRequest</name>
        <label>NewRequest</label>
        <locationX>50</locationX>
        <locationY>755</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>CustomNotificationRecord.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>RecipientIds</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <stringValue>New Competition Description Update was created</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <stringValue>Please take a look</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <name>Rejected</name>
        <label>Rejected</label>
        <locationX>578</locationX>
        <locationY>863</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>CustomNotificationRecord.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>RecipientIds</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <stringValue>Competition Description Update was updated</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <stringValue>Your suggestion was rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <assignments>
        <name>AssignRecipientCompetitionOwner</name>
        <label>Assign Recipient [Competition Owner]</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>RecipientIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.Competition__r.CreatedBy.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>NewRequest</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignRecipientRecordOwner</name>
        <label>Assign Recipient [Record Owner]</label>
        <locationX>578</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>RecipientIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.CreatedById</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Status_of_Request</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>isNewRecord</name>
        <label>isNewRecord</label>
        <locationX>578</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isNewRequest</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignRecipientCompetitionOwner</targetReference>
            </connector>
            <label>isNewRequest</label>
        </rules>
        <rules>
            <name>isChanged</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignRecipientRecordOwner</targetReference>
            </connector>
            <label>isChanged</label>
        </rules>
    </decisions>
    <decisions>
        <name>Status_of_Request</name>
        <label>Status of Request</label>
        <locationX>578</locationX>
        <locationY>755</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsApproved</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Approved</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateDesctiption</targetReference>
            </connector>
            <label>IsApproved</label>
        </rules>
        <rules>
            <name>isRejected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rejected</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Rejected</targetReference>
            </connector>
            <label>isRejected</label>
        </rules>
    </decisions>
    <description>Update related Competition Description and send notifications</description>
    <environments>Default</environments>
    <formulas>
        <name>isNew</name>
        <dataType>Boolean</dataType>
        <expression>isNew()</expression>
    </formulas>
    <formulas>
        <name>ReceipientIds</name>
        <dataType>String</dataType>
        <expression>{!$Record.Competition__r.CreatedById}</expression>
    </formulas>
    <interviewLabel>Update related Competition Description and send notifications {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[CMP] Competition Update Description</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>CompetitionRecord</name>
        <label>Competition Record</label>
        <locationX>578</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>isNewRecord</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Competition__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>cmp_Competition__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>CustomNotificationRecord</name>
        <label>Custom Notification Record</label>
        <locationX>578</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CompetitionRecord</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>cmp_DescriptionUpdate</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateDesctiption</name>
        <label>UpdateDesctiption</label>
        <locationX>314</locationX>
        <locationY>863</locationY>
        <connector>
            <targetReference>Approved</targetReference>
        </connector>
        <inputAssignments>
            <field>CompetitionDescription__c</field>
            <value>
                <elementReference>$Record.Description__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Competition__r</inputReference>
    </recordUpdates>
    <start>
        <locationX>452</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>CustomNotificationRecord</targetReference>
        </connector>
        <object>cmp_Description__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>RecipientIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>

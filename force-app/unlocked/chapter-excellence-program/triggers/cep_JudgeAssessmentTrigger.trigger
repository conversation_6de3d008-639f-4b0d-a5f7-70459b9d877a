trigger cep_JudgeAssessmentTrigger on cep_AssessmentJudge__c(after insert, after update, after delete) {
    if (Trigger.isAfter && Trigger.isInsert) {
        cep_JudgeAssessmentTriggerHandler.afterInsert(Trigger.new);
    } else if (Trigger.isAfter && Trigger.isUpdate) {
        cep_JudgeAssessmentTriggerHandler.afterUpdate(Trigger.new);
    } else if (Trigger.isAfter && Trigger.isDelete) {
        cep_JudgeAssessmentTriggerHandler.afterDelete(Trigger.old);
    }
}

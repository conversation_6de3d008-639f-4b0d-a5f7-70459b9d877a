public with sharing class cep_JudgeAssessmentTriggerHandler {
    private static final String ASSESSMENT_STATUS_NEW = 'New';
    private static final String ASSESSMENT_STATUS_DONE = 'Done';

    private final static String READY_FOR_ASSESSMENT_STATUS = 'Ready for assessment';
    private final static String DONE_ASSESSMENT_STATUS = 'Done';

    private without sharing class WS_ShareSelector {
        private List<ChapterExcellenceProgram__Share> getChapterShares(List<Id> parentIds, List<Id> userIds) {
            return [
                SELECT Id, ParentId, UserOrGroupId
                FROM ChapterExcellenceProgram__Share
                WHERE
                    ParentId IN :parentIds
                    AND UserOrGroupId IN :userIds
                    AND RowCause = :cep_ChapterShareHelper.MANUAL_ROW_CAUSE
            ];
        }

        private List<AccountShare> getAccountShares(List<Id> accountIds, List<Id> userIds) {
            return [
                SELECT Id, AccountId, UserOrGroupId
                FROM AccountShare
                WHERE
                    AccountId IN :accountIds
                    AND UserOrGroupId IN :userIds
                    AND RowCause = :cep_ChapterShareHelper.MANUAL_ROW_CAUSE
            ];
        }
    }

    private without sharing class WS_ChapterSelector {
        private Map<Id, ChapterExcellenceProgram__c> getChaptersInfo(List<Id> chapterIds) {
            return new Map<Id, ChapterExcellenceProgram__c>(
                [
                    SELECT Id, NameOfSchool__c
                    FROM ChapterExcellenceProgram__c
                    WHERE Id IN :chapterIds
                ]
            );
        }
    }

    private without sharing class WS_AssessmentJudgeSelector {
        private List<cep_AssessmentJudge__c> getAssessmentJudges(Set<Id> contactIds) {
            return [
                SELECT Id, Contact__c, ChapterExcellenceProgram__c
                FROM cep_AssessmentJudge__c
                WHERE Contact__c IN :contactIds AND AssessmentStatus__c = :ASSESSMENT_STATUS_NEW
            ];
        }

        private List<cep_AssessmentJudge__c> getAssessmentJudgesByCep(Set<String> chapterIds) {
            return [
                SELECT Id, Contact__c, ChapterExcellenceProgram__c, AssessmentStatus__c
                FROM cep_AssessmentJudge__c
                WHERE ChapterExcellenceProgram__c IN :chapterIds AND AssessmentStatus__c = :ASSESSMENT_STATUS_NEW
            ];
        }
    }

    private without sharing class WS_PermissionSetAssignmentSelector {
        private List<PermissionSetAssignment> getPermissionSetAssignments(List<Id> userIds, String permissionSetId) {
            return [
                SELECT Id, AssigneeId
                FROM PermissionSetAssignment
                WHERE AssigneeId IN :userIds AND PermissionSetId = :permissionSetId
            ];
        }
    }

    public static void afterInsert(List<cep_AssessmentJudge__c> judges) {
        changeStatus(judges);
        shareRecords(judges);
    }

    public static void afterUpdate(List<cep_AssessmentJudge__c> judges) {
        changeStatus(judges);
        removeSharesAfterDoneStatus(judges);
    }

    public static void afterDelete(List<cep_AssessmentJudge__c> judgesOld) {
        changeStatus(judgesOld);
        removeShareRecords(judgesOld);
    }

    @Future
    public static void insertPermissionSetAssigments(List<Id> userIds) {
        List<PermissionSetAssignment> psa = generatePermissionSetAssigments(userIds);
        WS.insertRecords(psa);
    }

    @Future
    public static void deletePermissionSetAssigments(Map<Id, Id> contactIdToUserId, List<Id> chapterIds) {
        List<PermissionSetAssignment> psa = getPermissionSetAssignmentsToDelete(contactIdToUserId);
        WS.deleteRecords(psa);
    }

    private static void changeStatus(List<cep_AssessmentJudge__c> judges) {
        List<ChapterExcellenceProgram__c> updatedChapters = new List<ChapterExcellenceProgram__c>();
        Set<String> chapterIds = new Set<String>();
        Set<Id> chapterIdsWithSuccessAssessments = new Set<Id>();
        Set<Id> chapterIdsWithReadyToAssessment = new Set<Id>();

        for (cep_AssessmentJudge__c judge : judges) {
            if (judge.ChapterExcellenceProgram__c != null) {
                chapterIds.add(judge.ChapterExcellenceProgram__c);
            }
        }

        if (chapterIds.size() == 0) {
            return;
        }

        List<cep_AssessmentJudge__c> assessmentJudges = new WS_AssessmentJudgeSelector()
            .getAssessmentJudgesByCep(chapterIds);

        for (cep_AssessmentJudge__c chapterJudge : assessmentJudges) {
            if (chapterJudge.AssessmentStatus__c == DONE_ASSESSMENT_STATUS) {
                chapterIdsWithSuccessAssessments.add(chapterJudge.ChapterExcellenceProgram__c);
            } else {
                chapterIdsWithReadyToAssessment.add(chapterJudge.ChapterExcellenceProgram__c);
            }
        }

        for (String chapterId : chapterIds) {
            updatedChapters.add(
                new ChapterExcellenceProgram__c(
                    Id = chapterId,
                    Status__c = !chapterIdsWithReadyToAssessment.contains(chapterId)
                        ? DONE_ASSESSMENT_STATUS
                        : READY_FOR_ASSESSMENT_STATUS
                )
            );
        }

        if (updatedChapters.size() > 0) {
            WS.updateRecords(updatedChapters);
        }
    }

    private static void shareRecords(List<cep_AssessmentJudge__c> judges) {
        List<ChapterExcellenceProgram__Share> chapterShares = new List<ChapterExcellenceProgram__Share>();
        List<Id> userIds = new List<Id>();
        List<Id> chapterIds = new List<Id>();
        Map<Id, Id> contactIdToUserId = generateContactWithUserMap(judges);

        for (cep_AssessmentJudge__c judge : judges) {
            Id userId = contactIdToUserId.get(judge.Contact__c);
            if (
                userId != null &&
                judge.ChapterExcellenceProgram__c != null &&
                judge.AssessmentStatus__c == ASSESSMENT_STATUS_NEW
            ) {
                chapterShares.add(cep_ChapterShareHelper.createShare(judge.ChapterExcellenceProgram__c, userId));
                chapterIds.add(judge.ChapterExcellenceProgram__c);
                userIds.add(userId);
            }
        }

        WS.insertRecords(chapterShares, false);
        shareSchoolAccountRecords(chapterShares, chapterIds);
        insertPermissionSetAssigments(userIds);
    }

    private static void shareSchoolAccountRecords(
        List<ChapterExcellenceProgram__Share> chapterShares,
        List<Id> chapterIds
    ) {
        List<AccountShare> accountShares = new List<AccountShare>();
        Map<Id, ChapterExcellenceProgram__c> chaptersInfo = new WS_ChapterSelector().getChaptersInfo(chapterIds);

        for (ChapterExcellenceProgram__Share chapterShare : chapterShares) {
            accountShares.add(
                new AccountShare(
                    AccountId = chaptersInfo.get(chapterShare.ParentId).NameOfSchool__c,
                    UserOrGroupId = chapterShare.UserOrGroupId,
                    AccountAccessLevel = cep_ChapterShareHelper.READ_ACCESS_LEVEL,
                    OpportunityAccessLevel = cep_ChapterShareHelper.NONE_ACCESS_LEVEL,
                    CaseAccessLevel = cep_ChapterShareHelper.NONE_ACCESS_LEVEL,
                    RowCause = cep_ChapterShareHelper.MANUAL_ROW_CAUSE
                )
            );
        }

        WS.insertRecords(accountShares, false);
    }

    private static List<PermissionSetAssignment> generatePermissionSetAssigments(List<Id> userIds) {
        List<PermissionSetAssignment> permissionSetAssignments = new List<PermissionSetAssignment>();
        String permissionSetName = Test.isRunningTest()
            ? cep_TestUtils.JUDGE_PERMISSION_SET_NAME
            : cep_Settings__c.getInstance().AssessmentJudgePermissionSetName__c;

        Id judgePermissionSetId = [SELECT Id FROM PermissionSet WHERE Name = :permissionSetName].Id;

        Set<String> userWithPermissionSet = getUsersWithPermissionSet(userIds, judgePermissionSetId);

        for (Id userId : userIds) {
            if (!userWithPermissionSet.contains(userId)) {
                PermissionSetAssignment psa = new PermissionSetAssignment(
                    PermissionSetId = judgePermissionSetId,
                    AssigneeId = userId
                );

                permissionSetAssignments.add(psa);
            }
        }

        return permissionSetAssignments;
    }

    private static Set<String> getUsersWithPermissionSet(List<Id> userIds, String permissionSetId) {
        Set<String> userWithPermissionSetIds = new Set<String>();
        List<PermissionSetAssignment> permissionSetAssignments = new WS_PermissionSetAssignmentSelector()
            .getPermissionSetAssignments(userIds, permissionSetId);

        for (PermissionSetAssignment psa : permissionSetAssignments) {
            userWithPermissionSetIds.add(psa.AssigneeId);
        }

        return userWithPermissionSetIds;
    }

    private static void removeSharesAfterDoneStatus(List<cep_AssessmentJudge__c> judges) {
        List<cep_AssessmentJudge__c> judgeToDeleteShares = new List<cep_AssessmentJudge__c>();

        for (cep_AssessmentJudge__c judge : judges) {
            if (judge.AssessmentStatus__c == ASSESSMENT_STATUS_DONE) {
                judgeToDeleteShares.add(judge);
            }
        }

        removeShareRecords(judgeToDeleteShares);
    }

    private static void removeShareRecords(List<cep_AssessmentJudge__c> judgesOld) {
        Map<Id, Id> contactIdToUserId = generateContactWithUserMap(judgesOld);
        List<Id> chapterIds = new List<Id>();

        for (cep_AssessmentJudge__c judge : judgesOld) {
            if (judge.ChapterExcellenceProgram__c != null) {
                chapterIds.add(judge.ChapterExcellenceProgram__c);
            }
        }

        List<ChapterExcellenceProgram__Share> sharesToDelete = new WS_ShareSelector()
            .getChapterShares(chapterIds, contactIdToUserId.values());

        if (!sharesToDelete.isEmpty()) {
            WS.deleteRecords(sharesToDelete);
            removeSchoolAccountShareRecords(chapterIds, contactIdToUserId.values());
            deletePermissionSetAssigments(contactIdToUserId, chapterIds);
        }
    }

    private static void removeSchoolAccountShareRecords(List<Id> chapterIds, List<Id> userIds) {
        Map<Id, ChapterExcellenceProgram__c> chaptersInfo = new WS_ChapterSelector().getChaptersInfo(chapterIds);
        List<Id> schoolAccountIds = new List<Id>();

        for (ChapterExcellenceProgram__c chapter : chaptersInfo.values()) {
            schoolAccountIds.add(chapter.NameOfSchool__c);
        }

        List<AccountShare> accountSharesToDelete = new WS_ShareSelector().getAccountShares(schoolAccountIds, userIds);

        WS.deleteRecords(accountSharesToDelete);
    }

    private static List<PermissionSetAssignment> getPermissionSetAssignmentsToDelete(Map<Id, Id> contactIdToUserId) {
        String permissionSetName = Test.isRunningTest()
            ? cep_TestUtils.JUDGE_PERMISSION_SET_NAME
            : cep_Settings__c.getInstance().AssessmentJudgePermissionSetName__c;
        Id judgePermissionSetId = [SELECT Id FROM PermissionSet WHERE Name = :permissionSetName].Id;

        List<cep_AssessmentJudge__c> assessmentJudges = new WS_AssessmentJudgeSelector()
            .getAssessmentJudges(contactIdToUserId.keySet());
        Map<Id, Id> usersToDeletePS = new Map<Id, Id>(contactIdToUserId);

        for (cep_AssessmentJudge__c aj : assessmentJudges) {
            Boolean isNotNullFields = aj.ChapterExcellenceProgram__c != null && aj.Contact__c != null;

            if (isNotNullFields) {
                usersToDeletePS.remove(aj.Contact__c);
            }
        }

        return new WS_PermissionSetAssignmentSelector()
            .getPermissionSetAssignments(usersToDeletePS.values(), judgePermissionSetId);
    }

    private static Map<Id, Id> generateContactWithUserMap(List<cep_AssessmentJudge__c> judges) {
        Set<String> contactIds = new Set<String>();
        for (cep_AssessmentJudge__c judge : judges) {
            if (judge.Contact__c != null) {
                contactIds.add(judge.Contact__c);
            }
        }

        Map<Id, Id> contactIdWithUserId = new Map<Id, Id>();
        for (User user : [SELECT Id, ContactId FROM User WHERE ContactId IN :contactIds]) {
            contactIdWithUserId.put(user.ContactId, user.Id);
        }

        return contactIdWithUserId;
    }
}
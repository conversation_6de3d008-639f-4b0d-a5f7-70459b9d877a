public with sharing class cep_RecordDetailService {
    private static final String APP_REPRESENT_WORK_PICKLIST_NAME = 'ApplicationRepresentsTheWorkOf__c';
    private static final String APP_REPRESENT_WORK_TEXT_NAME = 'ApplicationRepresentsTheWorkOfAText__c';
    private static final String NUMBER_OF_UNDUPLICATED_STUDENTS_NAME = 'NumberOfUnduplicatedStudent__c';

    private static final String READ_ONLY_MODE = 'readonly';
    private static final String EDIT_MODE = 'edit';

    public static final String NEW_STATUS = 'New';
    public static final String READY_FOR_MEMBERS_STATUS = 'Ready for members';
    public static final String NEED_CHANGES_FROM_MEMBERS_STATUS = 'Needed changes from members';

    public static ChapterInformation getChapterInfo(String recordId) {
        ChapterExcellenceProgram__c chapter = getChapterExcelleceProgram(recordId);
        Id contactId = PortalUser.getContactId();
        Boolean isLeadAdvisor = contactId == chapter.ChapterAdvisor__r.Id;

        return generateChapterInfo(chapter, contactId, isLeadAdvisor);
    }

    private static ChapterInformation generateChapterInfo(
        ChapterExcellenceProgram__c chapter,
        Id contactId,
        Boolean isLeadAdvisor
    ) {
        ChapterInformation chapterInfo = new ChapterInformation();
        Boolean isJudge = FeatureManagement.checkPermission(cep_Constant.SEP_CAN_RATE_FORM);
        String periodName = isJudge && !isLeadAdvisor ? cep_Constant.RATE_DEADLINE : cep_Constant.SUBMIT_DEADLINE;
        cep_ChapterService.DateInfo dateInfo = cep_ChapterService.generateDateInfo(periodName);

        if (dateInfo.currentDate > dateInfo.endDate) {
            chapterInfo.isExpired = true;
            chapterInfo.errorMessage = isJudge && !isLeadAdvisor
                ? System.Label.cepErrorExpiredChapterMessageJudge
                : System.Label.cepErrorExpiredChapterMessageMembers;
            return chapterInfo;
        }

        Boolean canEditForm = checkFormEditPermission(chapter, contactId);
        Boolean canEditChapterInfoSection = isLeadAdvisor && canEditForm;
        Map<String, FieldInfo> fieldNameToFieldInfo = generateChapterInfoFields(chapter, canEditChapterInfoSection);

        chapterInfo.formMode = canEditForm ? EDIT_MODE : READ_ONLY_MODE;
        chapterInfo.readOnlyFields = generateReadOnlyFields(chapter);
        chapterInfo.chapterRepresentPicklist = fieldNameToFieldInfo.get(APP_REPRESENT_WORK_PICKLIST_NAME);
        chapterInfo.chapterRepresentText = fieldNameToFieldInfo.get(APP_REPRESENT_WORK_TEXT_NAME);
        chapterInfo.numberOfUnduplicatedStudent = fieldNameToFieldInfo.get(NUMBER_OF_UNDUPLICATED_STUDENTS_NAME);
        chapterInfo.isSchoolUser = chapter.NameOfSchool__c == PortalUser.getAccountId();
        chapterInfo.isLeadAdvisor = isLeadAdvisor;

        return chapterInfo;
    }

    private static Map<String, FieldInfo> generateChapterInfoFields(
        ChapterExcellenceProgram__c chapter,
        Boolean canEditFields
    ) {
        return new Map<String, FieldInfo>{
            APP_REPRESENT_WORK_PICKLIST_NAME => new FieldInfo(
                System.Label.cepRepresentTheWorkOfAFieldLabel,
                chapter.ApplicationRepresentsTheWorkOf__c,
                APP_REPRESENT_WORK_PICKLIST_NAME,
                !canEditFields
            ),
            APP_REPRESENT_WORK_TEXT_NAME => new FieldInfo(
                System.Label.cepRepresentTheWorkOfAFieldLabel,
                chapter.ApplicationRepresentsTheWorkOfAText__c,
                APP_REPRESENT_WORK_TEXT_NAME,
                !canEditFields
            ),
            NUMBER_OF_UNDUPLICATED_STUDENTS_NAME => new FieldInfo(
                System.Label.cepNumberOfUnduplicatedStudentFieldLabel,
                chapter.NumberOfUnduplicatedStudent__c,
                NUMBER_OF_UNDUPLICATED_STUDENTS_NAME,
                !canEditFields
            )
        };
    }

    private static List<FieldInfo> generateReadOnlyFields(ChapterExcellenceProgram__c chapter) {
        List<FieldInfo> readOnlyFields = new List<FieldInfo>();
        readOnlyFields.add(new FieldInfo(System.Label.cepNameOfSchoolFieldLabel, chapter.NameOfSchool__r.Name));
        readOnlyFields.add(new FieldInfo(System.Label.cepAdvisorNameFieldLabel, chapter.ChapterAdvisor__r.Name));
        readOnlyFields.add(new FieldInfo(System.Label.cepAdvisorEmailFieldLabel, chapter.ChapterAdvisor__r.Email));
        readOnlyFields.add(new FieldInfo(System.Label.cepCellPhoneFieldLabel, chapter.ChapterAdvisor__r.Phone));
        readOnlyFields.add(new FieldInfo(System.Label.cepSchoolPhoneFieldLabel, chapter.NameOfSchool__r.Phone));
        readOnlyFields.add(
            new FieldInfo(
                System.Label.cepTotalSkillUSAMembershipFieldLabel,
                String.valueOf(chapter.TotalSkillsUSAMembership__c)
            )
        );

        return readOnlyFields;
    }

    private static Boolean checkFormEditPermission(ChapterExcellenceProgram__c chapter, Id contactId) {
        if (chapter.Status__c == NEW_STATUS) {
            return chapter.ChapterAdvisor__r.Id == contactId;
        } else if (
            chapter.Status__c == READY_FOR_MEMBERS_STATUS ||
            chapter.Status__c == NEED_CHANGES_FROM_MEMBERS_STATUS
        ) {
            return chapter.ChapterPresident__c == contactId || chapter.ChapterSecretary__c == contactId;
        }

        return false;
    }

    private static ChapterExcellenceProgram__c getChapterExcelleceProgram(Id recordId) {
        return [
            SELECT
                Id,
                Status__c,
                NameOfSchool__r.Name,
                NameOfSchool__r.Phone,
                ChapterAdvisor__r.Name,
                ChapterAdvisor__r.Email,
                ChapterAdvisor__r.Phone,
                ChapterPresident__c,
                ChapterSecretary__c,
                TotalSkillsUSAMembership__c,
                ApplicationRepresentsTheWorkOf__c,
                ApplicationRepresentsTheWorkOfAText__c,
                NumberOfUnduplicatedStudent__c
            FROM ChapterExcellenceProgram__c
            WHERE Id = :recordId
            WITH USER_MODE
        ];
    }

    public static String getCurrentStatus(Id recordId) {
        return [SELECT Status__c FROM ChapterExcellenceProgram__c WHERE Id = :recordId].Status__c;
    }

    public class ChapterInformation {
        @AuraEnabled
        public FieldInfo chapterRepresentPicklist;
        @AuraEnabled
        public FieldInfo chapterRepresentText;
        @AuraEnabled
        public FieldInfo numberOfUnduplicatedStudent;
        @AuraEnabled
        public List<FieldInfo> readOnlyFields;
        @AuraEnabled
        public String formMode;
        @AuraEnabled
        public Boolean isExpired;
        @AuraEnabled
        public String errorMessage;
        @AuraEnabled
        public Boolean isSchoolUser;
        @AuraEnabled
        public Boolean isLeadAdvisor;
    }

    public class FieldInfo {
        public FieldInfo(String label, String value, String apiName, Boolean isDisabled) {
            this.label = label;
            this.value = value;
            this.apiName = apiName;
            this.isDisabled = isDisabled;
        }

        public FieldInfo(String label, String value) {
            this.label = label;
            this.value = value;
        }

        @AuraEnabled
        public String apiName;
        @AuraEnabled
        public String label;
        @AuraEnabled
        public String value;
        @AuraEnabled
        public Boolean isDisabled;
    }
}
@isTest
private class cep_JudgeAssessmentButtonCtrlTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createJudgeUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createJudgeUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact judgeContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Judge'
            );
            insert judgeContact;
            PortalUser.create(judgeContact);
        }
    }

    @IsTest
    static void getVisibilityInfoTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.RATE_DEADLINE, dayBefore + '-' + dayAfter);
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Boolean isVisible = false;
        Id recordId;
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(Status__c = 'Ready for assessment');
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
        }
        System.runAs(judgeUser) {
            isVisible = cep_JudgeAssessmentButtonCtrl.checkVisibility(recordId);
        }
        Test.stopTest();
        Assert.areEqual(true, isVisible, 'Wrong visibility value');
    }

    @IsTest
    static void getVisibilityInfoInvisibleTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        String dayAfter = DateTime.now().addDays(-2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.RATE_DEADLINE, dayBefore + '-' + dayAfter);
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Boolean isVisible = false;
        Id recordId;
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(Status__c = 'Ready for assessment');
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
        }
        System.runAs(judgeUser) {
            isVisible = cep_JudgeAssessmentButtonCtrl.checkVisibility(recordId);
        }
        Test.stopTest();
        Assert.areEqual(false, isVisible, 'Wrong visibility value');
    }

    @IsTest
    static void getVisibilityInfoErrorTest() {
        Boolean isError = false;
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        try {
            System.runAs(judgeUser) {
                cep_JudgeAssessmentButtonCtrl.checkVisibility('');
            }
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }
}
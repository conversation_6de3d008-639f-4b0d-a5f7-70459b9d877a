public with sharing class cep_UploadApplicationImagesService {

    private static final List<String> SKILLS_ACTIVITY_PHOTO_TITLES = new List<String> {
        System.Label.cepPersonalSkillActivityPhoto,
        System.Label.cepWorkplaceSkillActivityPhoto,
        System.Label.cepTechnicalSkillActivityPhoto
    };

    public static final String READY_FOR_MEMBERS_STATUS = 'Ready for members';
    public static final String NEED_CHANGES_FROM_MEMBERS_STATUS = 'Needed changes from members';
    
    public static ImagesInfo getAppImagesInfo(String recordId) {
        if (!cep_ChapterService.checkVisibility(recordId)) {
            return null;
        }
        
        ImagesInfo imagesInfo = new ImagesInfo();
        List<FileInfo> filesInfo = new List<FileInfo>();
        List<ContentDocument> activityPhotoDocuments = getChapterActivityPhoto(recordId);
        Map<String, ContentDocument> skillNameToActivityPhoto = new Map<String, ContentDocument>();
        
        for (ContentDocument document : activityPhotoDocuments) {
            skillNameToActivityPhoto.put(document.Title, document);
        }

        imagesInfo.hasEditPermission = checkEditPermission(recordId);
        imagesInfo.skillNameToActivityPhoto = skillNameToActivityPhoto;

        return imagesInfo;
    }
    
    public static void updateAndShareAppImage(String recordId, String jsonFileInfo) {
        FileInfo fileInfo = (FileInfo) JSON.deserialize(jsonFileInfo, FileInfo.class);
        updateImageDocument(fileInfo);
    }

    public static void deleteFile(String fileId) {
        if (String.isEmpty(fileId)) {
            return;
        }

        delete [
            SELECT Id
            FROM ContentDocument
            WHERE LatestPublishedVersionId = :fileId
        ];
    }

    private static void updateImageDocument(FileInfo fileInfo) {
        update new ContentDocument(Id = fileInfo.contentDocumentId, Title = fileInfo.skillTitle, Description = fileInfo.description);
    }

    private static List<ContentDocument> getChapterActivityPhoto(String recordId) {
        List<ContentDocumentLink> docLinks = getRelatedChapterDocLinks(recordId);
        List<String> documentIds = new List<String>();
        
        for (ContentDocumentLink docLink : docLinks) {
            documentIds.add(docLink.ContentDocumentId);
        }

        return getActivityPhotoByDocumentIds(documentIds);
    }

    private static List<ContentDocumentLink> getRelatedChapterDocLinks(String recordId) {
        return [
            SELECT Id, ContentDocumentId
            FROM ContentDocumentLink
            WHERE LinkedEntityId = :recordId
        ];
    }

    private static List<ContentDocument> getActivityPhotoByDocumentIds(List<String> documentIds) {
        return [
            SELECT Id, Title, LatestPublishedVersionId, Description
            FROM ContentDocument
            WHERE Id IN :documentIds
                AND Title IN :SKILLS_ACTIVITY_PHOTO_TITLES
                AND isDeleted = false
        ];
    }

    private static Boolean checkEditPermission(String chapterId) {
        Id contactId = PortalUser.getContactId();
        
        if (contactId == null) {
            return false;
        }

        ChapterExcellenceProgram__c chapter = getChapterExcellenceProgramById(chapterId);
        Boolean isMemberStatus = chapter.Status__c == READY_FOR_MEMBERS_STATUS || chapter.Status__c == NEED_CHANGES_FROM_MEMBERS_STATUS;

        return chapter != null && isMemberStatus && (
            chapter.ChapterPresident__c == contactId || chapter.ChapterSecretary__c == contactId
        );
    }

    private static ChapterExcellenceProgram__c getChapterExcellenceProgramById(String recordId) {
        return [
            SELECT Id, ChapterPresident__c, ChapterSecretary__c, Status__c
            FROM ChapterExcellenceProgram__c
            WHERE Id = :recordId
        ];
    }

    public class ImagesInfo {
        @AuraEnabled
        public Map<String, ContentDocument> skillNameToActivityPhoto;
        @AuraEnabled
        public Boolean hasEditPermission;
    }

    public class FileInfo {
        @AuraEnabled
        public String contentVersionId;
        @AuraEnabled
        public String contentDocumentId;
        @AuraEnabled
        public String skillTitle;
        @AuraEnabled
        public String description;
    }
}
@isTest
public class cep_ContactTriggerTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createLeadAdvisorUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createLeadAdvisorUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact theContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Lead Advisor'
            );
            insert theContact;
            PortalUser.create(theContact);
        }
    }

    @IsTest
    static void shareRecordsAfterInsertTest() {
        List<ChapterExcellenceProgram__c> chapters = new List<ChapterExcellenceProgram__c>();
        User leadAdvisor = [SELECT Id, ContactId, AccountId FROM User WHERE Email = '<EMAIL>'];
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                NameOfSchool__c = leadAdvisor.AccountId
            );
            insert cep;
            Contact contact = new Contact(Id = leadAdvisor.ContactId, FirstName = 'John');
            update contact;
        }
        Test.startTest();
        System.runAs(leadAdvisor) {
            chapters = [
                SELECT Id 
                FROM ChapterExcellenceProgram__c
                WHERE NameOfSchool__c = :leadAdvisor.AccountId
            ];
        }
        Test.stopTest();
        System.assertEquals(1, chapters.size(), 'Wrong chapters size');
    }

    @IsTest
    static void removeShareRecordsAfterUpdateTest() {
        User leadAdvisor = [SELECT Id, ContactId, AccountId FROM User WHERE Email = '<EMAIL>'];
        List<ChapterExcellenceProgram__c> chapters = new List<ChapterExcellenceProgram__c>();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c chapter = new ChapterExcellenceProgram__c();
            chapter.NameOfSchool__c = leadAdvisor.AccountId;
            insert chapter;
            Account newAccount = new Account(Name = 'Test Account');
            insert newAccount;
            Contact contact = new Contact(Id = leadAdvisor.ContactId, AccountId = newAccount.Id);
            update contact;
        }
        Test.startTest();
        System.runAs(leadAdvisor) {
            chapters = [
                SELECT Id 
                FROM ChapterExcellenceProgram__c
                WHERE ChapterSecretary__c = :leadAdvisor.ContactId
            ];
        }
        Test.stopTest();
        System.assertEquals(0, chapters.size(), 'Wrong chapters size');
    }
}
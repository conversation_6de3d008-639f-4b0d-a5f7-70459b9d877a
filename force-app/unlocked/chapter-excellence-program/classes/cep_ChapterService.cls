public with sharing class cep_ChapterService {
    private final static String ASSESSMENT_STATUS_NEW = 'New';
    private final static String READY_FOR_ASSESSMENT_STATUS = 'Ready for assessment';

    private without sharing class WS_JudgeSelector {
        private List<cep_AssessmentJudge__c> getNewJudgeRecord(Id recordId, Id contactId) {
            return [
                SELECT Id
                FROM cep_AssessmentJudge__c
                WHERE
                    ChapterExcellenceProgram__c = :recordId
                    AND ChapterExcellenceProgram__r.Status__c = :READY_FOR_ASSESSMENT_STATUS
                    AND Contact__c = :contactId
                    AND AssessmentStatus__c = :ASSESSMENT_STATUS_NEW
                LIMIT 1
            ];
        }
    }

    public static Boolean checkVisibility() {
        DateInfo dateInfo = generateDateInfo(cep_Constant.SUBMIT_DEADLINE);
        if (dateInfo.currentDate >= dateInfo.startDate && dateInfo.currentDate < dateInfo.endDate) {
            Boolean hasSubmitPermission = FeatureManagement.checkPermission(cep_Constant.SEP_CAN_SUBMIT_FORM);
            User userInfo = [SELECT ContactId, AccountId FROM User WHERE Id = :UserInfo.getUserId()];
            ChapterExcellenceProgram__c chapter = getActualChapter(dateInfo, userInfo.AccountId);

            if (chapter == null || userInfo.ContactId == null) {
                return false;
            }

            Boolean isMemberVisibility =
                chapter.ChapterPresident__c == userInfo.ContactId ||
                chapter.ChapterSecretary__c == userInfo.ContactId;
            Boolean isLeadAdvisorVisibility =
                chapter.ChapterAdvisor__c == userInfo.ContactId ||
                PortalUser.hasRole(PortalUser.Role.LEAD_ADVISOR) && hasSubmitPermission;

            return isLeadAdvisorVisibility || isMemberVisibility;
        }

        return false;
    }

    public static Boolean checkVisibility(Id chapterId) {
        Boolean memberVisibility = checkVisibility();
        return !memberVisibility ? checkJudgeVisibility(chapterId) : true;
    }

    public static Boolean checkJudgeVisibility(Id recordId) {
        Boolean hasRatePermission = FeatureManagement.checkPermission(cep_Constant.SEP_CAN_RATE_FORM);

        if (!hasRatePermission) {
            return false;
        }

        DateInfo dateInfo = generateDateInfo(cep_Constant.RATE_DEADLINE);

        if (dateInfo.currentDate >= dateInfo.startDate && dateInfo.currentDate < dateInfo.endDate) {
            Id contactId = PortalUser.getContactId();

            if (contactId == null) {
                return false;
            }

            List<cep_AssessmentJudge__c> newJudgeRecord = new WS_JudgeSelector().getNewJudgeRecord(recordId, contactId);
            return !newJudgeRecord.isEmpty();
        }

        return false;
    }

    public static Date generateDate(String formattedDate, Integer year) {
        return Date.parse(formattedDate.split('/')[1] + '/' + formattedDate.split('/')[0] + '/' + year);
    }

    public static DateInfo generateDateInfo(String periodSettingName) {
        String submitDeadline = (String) cep_Settings__c.getOrgDefaults().get(periodSettingName);

        if (String.isEmpty(submitDeadline)) {
            return null;
        }

        List<String> deadlinePeriod = submitDeadline.replaceAll(' ', '').split('-');
        Date currentDate = Date.today();

        DateInfo dateInfo = new DateInfo();
        dateInfo.currentDate = currentDate;
        dateInfo.startDate = generateDate(deadlinePeriod[0], currentDate.year());
        dateInfo.endDate = generateDate(deadlinePeriod[1], currentDate.year());

        if (dateInfo.startDate > dateInfo.endDate) {
            dateInfo.startDate = dateInfo.startDate.addYears(-1);
        }

        return dateInfo;
    }

    public static ChapterExcellenceProgram__c getActualChapter(DateInfo dateInfo, Id accountId) {
        List<ChapterExcellenceProgram__c> result = [
            SELECT Id, ChapterPresident__c, ChapterSecretary__c, ChapterAdvisor__c
            FROM ChapterExcellenceProgram__c
            WHERE
                NameOfSchool__c = :accountId
                AND CreatedDate >= :dateInfo.startDate
                AND CreatedDate < :dateInfo.endDate
            LIMIT 1
        ];

        return result.isEmpty() ? null : result[0];
    }

    public static Id getChapterLeadAdvisorId(Id chapterId) {
        return [
            SELECT ChapterAdvisor__c
            FROM ChapterExcellenceProgram__c
            WHERE Id = :chapterId
        ]
        .ChapterAdvisor__c;
    }

    public class DateInfo {
        public Date startDate;
        public Date endDate;
        public Date currentDate;
    }
}
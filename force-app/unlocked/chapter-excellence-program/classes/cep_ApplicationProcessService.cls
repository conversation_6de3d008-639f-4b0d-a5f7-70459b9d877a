public with sharing class cep_ApplicationProcessService {
    public static final String NEW_STATUS = 'New';

    public static ProcessApplicationInfo createApplicationProcess() {
        ProcessApplicationInfo processAppInfo = new ProcessApplicationInfo();
        User userInfo = [SELECT ContactId, AccountId FROM User WHERE Id =: UserInfo.getUserId()];

        if (userInfo.ContactId == null) {
            return null;
        }

        Boolean hasSubmitPermission = FeatureManagement.checkPermission(cep_Constant.SEP_CAN_SUBMIT_FORM);
        cep_ChapterService.DateInfo dateInfo = cep_ChapterService.generateDateInfo(cep_Constant.SUBMIT_DEADLINE);
        ChapterExcellenceProgram__c chapter = cep_ChapterService.getActualChapter(dateInfo, userInfo.AccountId);

        if (PortalUser.hasRole(PortalUser.Role.LEAD_ADVISOR) && hasSubmitPermission) {
            processAppInfo.chapterId = chapter != null ? chapter.Id : createChapterExcellenceProgram(userInfo);
            processAppInfo.isNewChapter = chapter == null;
        } else if (chapter.ChapterPresident__c == userInfo.ContactId || chapter.ChapterSecretary__c == userInfo.ContactId) {
            processAppInfo.chapterId = chapter.Id;
            processAppInfo.isNewChapter = false;
        }
    
        return processAppInfo;
    }

    private static Id createChapterExcellenceProgram(User userInfo) {
        ChapterExcellenceProgram__c chapterExcellenceProgram = new ChapterExcellenceProgram__c(
            Status__c = NEW_STATUS,
            NameOfSchool__c = userInfo.AccountId,
            ChapterAdvisor__c = userInfo.ContactId
        );

        ChapterExcellenceProgram__c insertedCep = (ChapterExcellenceProgram__c) WS.insertRecord(chapterExcellenceProgram);
        
        return insertedCep.Id;
    }

    public class ProcessApplicationInfo {
        @AuraEnabled
        public Boolean isNewChapter;
        @AuraEnabled
        public String chapterId;
    }
}
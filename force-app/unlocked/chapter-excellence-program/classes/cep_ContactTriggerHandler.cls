public with sharing class cep_ContactTriggerHandler {
    private static final Map<PortalUser.Role, String> PORTAL_ROLES = PortalUser.ROLES;

    private without sharing class WS_ChapterExcellenceProgramSelector {
        private List<ChapterExcellenceProgram__c> getChaptersByAccountIds(Set<String> accountIds) {
            return [
                SELECT Id, NameOfSchool__c, ChapterAdvisor__c
                FROM ChapterExcellenceProgram__c
                WHERE NameOfSchool__c IN :accountIds
            ];
        }
    }

    private without sharing class WS_UserSelector {
        private List<User> getUsersByContactId(List<String> contactIds) {
            return [
                SELECT Id, ContactId, AccountId
                FROM User 
                WHERE ContactId IN :contactIds
                    AND isActive = true
            ];
        }
    }

    public static void afterInsert(List<Contact> contacts) {
        changeCountOfMembershipField(contacts);
        shareRecords(contacts);
    }

    public static void afterUpdate(List<Contact> contacts, Map<Id, Contact> contactOldMap) {
        changeCountOfMembershipField(contacts);
        removeShareRecords(contacts, contactOldMap);
        shareRecords(contacts);
    }

    private static void changeCountOfMembershipField(List<Contact> contacts) {
        Map<String, Integer> accountIdToStudentCount = new Map<String, Integer>();
        List<ChapterExcellenceProgram__c> chaptersToUpdate = new List<ChapterExcellenceProgram__c>();

        for (Contact contact : contacts) {
            if (!accountIdToStudentCount.containsKey(contact.AccountId)) {
                accountIdToStudentCount.put(contact.AccountId, 0);
            }

            if (contact.PortalRole__c == PORTAL_ROLES.get(PortalUser.Role.STUDENT)) {
                accountIdToStudentCount.put(contact.AccountId, accountIdToStudentCount.get(contact.AccountId) + 1);
            }
        }

        List<ChapterExcellenceProgram__c> chapters = new WS_ChapterExcellenceProgramSelector()
            .getChaptersByAccountIds(accountIdToStudentCount.keySet());

        for (ChapterExcellenceProgram__c chapter : chapters) {
            if (chapter.NameOfSchool__c != null) {
                chaptersToUpdate.add(new ChapterExcellenceProgram__c(
                    Id = chapter.Id,
                    TotalSkillsUSAMembership__c = accountIdToStudentCount.get(chapter.NameOfSchool__c)
                ));
            }
        }

        WS.updateRecords(chaptersToUpdate);
    }

    private static void shareRecords(List<Contact> contacts) {
        Map<String, List<String>> accountIdToContactIds = getAccountIdToContactIds(contacts);

        if (accountIdToContactIds.isEmpty()) {
            return;
        }

        List<ChapterExcellenceProgram__c> chapters = new WS_ChapterExcellenceProgramSelector()
            .getChaptersByAccountIds(accountIdToContactIds.keySet());

        Map<String, String> contactIdToUserId = generateContactIdWithUserIdMap(accountIdToContactIds.values());
        List<ChapterExcellenceProgram__Share> chapterShares = generateChapterShares(chapters, accountIdToContactIds, contactIdToUserId);

        WS.insertRecords(chapterShares);
    }

    private static void removeShareRecords(List<Contact> contacts, Map<Id, Contact> contactOldMap) {
        List<String> contactToDelete = new List<String>();
        String leadAdvisorRole = PORTAL_ROLES.get(PortalUser.Role.LEAD_ADVISOR);

        for (Contact contact : contacts) {
            String oldPortalRole = contactOldMap.get(contact.Id).PortalRole__c;
            Boolean isNotLeadAdvisor = oldPortalRole == leadAdvisorRole && contact.PortalRole__c != leadAdvisorRole;
            Boolean isNewAccount = contactOldMap.get(contact.Id).AccountId != contact.AccountId;

            if (isNotLeadAdvisor || isNewAccount) {
                contactToDelete.add(contact.Id);
            }
        }

        List<User> usersToDeleteShare = new WS_UserSelector().getUsersByContactId(contactToDelete);

        if (usersToDeleteShare.size() > 0) {
            List<String> userIds = new List<String>();

            for (User user : usersToDeleteShare) {
                userIds.add(user.Id);
            }

            List<ChapterExcellenceProgram__Share> sharesToDelete = new cep_ChapterShareHelper.WS_ShareSelector().getChapterSharesByUserIds(userIds);
            WS.deleteRecords(sharesToDelete);
        }
    }
    
    private static Map<String, List<String>> getAccountIdToContactIds(List<Contact> contacts) {
        Map<String, List<String>> accountIdToContactIds = new Map<String, List<String>>();
        for (Contact contact : contacts) {
            if (contact.PortalRole__c != null && contact.PortalRole__c.contains(PORTAL_ROLES.get(PortalUser.Role.LEAD_ADVISOR))) {
                if (!accountIdToContactIds.containsKey(contact.AccountId)) {
                    accountIdToContactIds.put(contact.AccountId, new List<String>());
                }
                accountIdToContactIds.get(contact.AccountId).add(contact.Id);
            }
        }
        return accountIdToContactIds;
    }

    private static List<ChapterExcellenceProgram__Share> generateChapterShares(
        List<ChapterExcellenceProgram__c> chapters,
        Map<String, List<String>> accountIdToContactIds,
        Map<String, String> contactIdToUserId
    ) {
        List<ChapterExcellenceProgram__Share> chapterShares = new List<ChapterExcellenceProgram__Share>();
        for (ChapterExcellenceProgram__c chapter : chapters) {
            if (!accountIdToContactIds.containsKey(chapter.NameOfSchool__c) || contactIdToUserId.containsKey(chapter.ChapterAdvisor__c)) {
                continue;
            }

            for (String contactId : accountIdToContactIds.get(chapter.NameOfSchool__c)) {
                String userId = contactIdToUserId.get(contactId);
                if (userId != null) {
                    chapterShares.add(cep_ChapterShareHelper.createShare(chapter.Id, userId));
                }
            }
        }
        return chapterShares;
    }

    private static Map<String, String> generateContactIdWithUserIdMap(List<List<String>> contactListIds) {
        Map<String, String> contactIdToUserId = new Map<String, String>();
        List<String> allContactIds = new List<String>();

        for (List<String> contactIds : contactListIds) {
            allContactIds.addAll(contactIds);
        }

        List<User> users = new WS_UserSelector().getUsersByContactId(allContactIds);

        for (User user : users) {
            contactIdToUserId.put(user.ContactId, user.Id);
        }

        return contactIdToUserId;
    }
}
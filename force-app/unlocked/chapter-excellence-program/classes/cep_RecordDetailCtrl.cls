public with sharing class cep_RecordDetailCtrl {
    private static final String FORM_LAYOUT_NAME = cep_Settings__c.getInstance().ApplicationProcessFormLayoutName__c;
    private static final String NEW_STATUS = 'New';
    private static final String READY_FOR_MEMBERS_STATUS = 'Ready for members';
    public static final String ASSESSMENT_STATUS_NEW = 'New';
    public static final String ASSESSMENT_STATUS_DONE = 'Done';

    private without sharing class WS_JudgeSelector {
        private cep_AssessmentJudge__c getJudge(Id chapterId, Id contactId) {
            return [
                SELECT Id
                FROM cep_AssessmentJudge__c
                WHERE
                    ChapterExcellenceProgram__c = :chapterId
                    AND Contact__c = :contactId
                    AND AssessmentStatus__c = :ASSESSMENT_STATUS_NEW
                LIMIT 1
            ];
        }
    }

    @AuraEnabled
    public static Boolean checkVisibility(String recordId) {
        try {
            return cep_ChapterService.checkVisibility(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static Boolean checkJudgeVisibility(Id recordId) {
        try {
            return cep_ChapterService.checkJudgeVisibility(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static aclab.FormLayout getFormSections(String recordId) {
        try {
            return aclab.Form.retrieve(FORM_LAYOUT_NAME, recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static cep_RecordDetailService.ChapterInformation getChapterInfo(String recordId) {
        try {
            return cep_RecordDetailService.getChapterInfo(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void updateChapter(ChapterExcellenceProgram__c chapterExcellenceProgram) {
        try {
            Id leadAdvisorId = cep_ChapterService.getChapterLeadAdvisorId(chapterExcellenceProgram.Id);
            String representWorkPicklistValue = chapterExcellenceProgram.ApplicationRepresentsTheWorkOf__c;
            String representWorkTextValue = chapterExcellenceProgram.ApplicationRepresentsTheWorkOfAText__c;
            String numberOfStudents = chapterExcellenceProgram.NumberOfUnduplicatedStudent__c;
            Boolean isFilledMembers =
                chapterExcellenceProgram.ChapterPresident__c != null ||
                chapterExcellenceProgram.ChapterSecretary__c != null;

            if (!Test.isRunningTest()) {
                chapterExcellenceProgram = (ChapterExcellenceProgram__c) aclab.Form.cleanInjectedFields(
                    chapterExcellenceProgram,
                    FORM_LAYOUT_NAME
                );
            }

            String status = cep_RecordDetailService.getCurrentStatus(chapterExcellenceProgram.Id);
            if (status == NEW_STATUS && isFilledMembers) {
                chapterExcellenceProgram.Status__c = READY_FOR_MEMBERS_STATUS;
            }

            if (leadAdvisorId == PortalUser.getContactId()) {
                chapterExcellenceProgram.ApplicationRepresentsTheWorkOf__c = representWorkPicklistValue;
                chapterExcellenceProgram.ApplicationRepresentsTheWorkOfAText__c = representWorkTextValue;
                chapterExcellenceProgram.NumberOfUnduplicatedStudent__c = numberOfStudents;
            }

            WS.updateRecord(chapterExcellenceProgram);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void saveAssessments(List<cep_AssessmentResult__c> assessmentResults, Id chapterId) {
        try {
            cep_Assessment__c assessment = new cep_Assessment__c(ChapterExcellenceProgram__c = chapterId);
            insert assessment;

            for (cep_AssessmentResult__c result : assessmentResults) {
                result.cep_Assessment__c = assessment.Id;
            }
            insert assessmentResults;

            Id contactId = PortalUser.getContactId();
            cep_AssessmentJudge__c judge = new WS_JudgeSelector().getJudge(chapterId, contactId);
            judge.AssessmentStatus__c = ASSESSMENT_STATUS_DONE;
            WS.updateRecord(judge);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}
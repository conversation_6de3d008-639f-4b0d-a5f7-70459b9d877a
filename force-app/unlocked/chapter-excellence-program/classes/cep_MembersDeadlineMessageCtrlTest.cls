@IsTest
public class cep_MembersDeadlineMessageCtrlTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createLeadAdvisorUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createLeadAdvisorUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact leadAdvisorContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Lead Advisor'
            );
            insert leadAdvisorContact;
            PortalUser.create(leadAdvisorContact);
        }
    }

    @IsTest
    static void getDeadlineDaysTest() {
        String dayBefore = DateTime.now().addDays(-1).format('dd/MM');
        String dayAfter = DateTime.now().addDays(10).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore + '-' + dayAfter);
        Integer deadlineDays;
        User leadAdvisor = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c chapter = new ChapterExcellenceProgram__c();
            chapter.ChapterSecretary__c = leadAdvisor.ContactId;
            insert chapter;
        }
        Test.startTest();
        System.runAs(leadAdvisor) {
            deadlineDays = cep_MembersDeadlineMessageCtrl.getDeadlineDays(); 
        }
        Test.stopTest();
        System.assertEquals(10, deadlineDays, 'Wrong deadline days');
    }

    @IsTest
    static void getDeadlineDaysNullTest() {
        Test.startTest();
        Integer deadlineDays = cep_MembersDeadlineMessageCtrl.getDeadlineDays(); 
        Test.stopTest();
        System.assertEquals(null, deadlineDays, 'Wrong deadline days');
    }

    @IsTest
    static void getDeadlineDaysErrorTest() {
        String dayBefore = DateTime.now().addDays(-1).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore);
        Integer deadlineDays;
        Boolean isError = false;
        User leadAdvisor = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(leadAdvisor) {
            try {
                deadlineDays = cep_MembersDeadlineMessageCtrl.getDeadlineDays(); 
            } catch (Exception e) {
                isError = true;
            }
        }
        Test.stopTest();
        System.assertEquals(true, isError, 'Wrong isError value');
    }
}
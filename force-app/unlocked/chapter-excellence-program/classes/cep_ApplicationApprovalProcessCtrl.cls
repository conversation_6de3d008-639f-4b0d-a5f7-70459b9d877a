public with sharing class cep_ApplicationApprovalProcessCtrl {
    @AuraEnabled(Cacheable=false)
    public static cep_ApplicationApprovalProcessService.ApprovalProcessVisibility getVisibilityInfo(String recordId) {
        try {
            return cep_ApplicationApprovalProcessService.getVisibilityInfo(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void changeChapterStatus(String recordId, Boolean isApprove, String comment) {
        try {
            cep_ApplicationApprovalProcessService.changeStatus(recordId, isApprove, comment);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}
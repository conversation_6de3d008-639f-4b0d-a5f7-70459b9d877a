public with sharing class cep_MembersDeadlineMessageService {
    private static final Integer DIFFERENCE_DAYS_LIMIT = 30; 

    public static Integer getDeadlineDays() {
        Id contactId = PortalUser.getContactId();

        if (contactId == null) {
            return null;
        }

        ChapterExcellenceProgram__c chapter = getMembersChapter(contactId);

        return chapter != null ? calculateDeadlineDays() : null;
    }

    private static Integer calculateDeadlineDays() {
        cep_ChapterService.DateInfo dateInfo = cep_ChapterService.generateDateInfo(cep_Constant.SUBMIT_DEADLINE);

        if (dateInfo.currentDate.daysBetween(dateInfo.endDate) > DIFFERENCE_DAYS_LIMIT || dateInfo.startDate > dateInfo.currentDate) {
            return null;
        }
 
        return dateInfo.currentDate.daysBetween(dateInfo.endDate);
    }

    private static ChapterExcellenceProgram__c getMembersChapter(Id contactId) {
        return [
            SELECT Id
            FROM ChapterExcellenceProgram__c 
            WHERE ChapterPresident__c = :contactId
                OR ChapterSecretary__c = :contactId
            WITH USER_MODE
            LIMIT 1
        ];
    }
}
public with sharing class cep_ExcellenceProgramsTriggerHandler {
    private static final String LEAD_ADVISOR_PORTAL_ROLE = 'Lead Advisor';
    private static final String STUDENT_PORTAL_ROLE = 'Student';
    private static final List<String> LOOKUP_FIELDS_FOR_SHARE = new List<String> {
        'ChapterSecretary__c',
        'ChapterPresident__c'
    };

    public static void afterInsert(Map<Id, ChapterExcellenceProgram__c> idToChapterNew) {
        changeCountOfMembershipField(idToChapterNew.values());
        shareRecords(idToChapterNew);
    }

    public static void afterUpdate(
        Map<Id, ChapterExcellenceProgram__c> idToChapterNew,
        Map<Id, ChapterExcellenceProgram__c> idToChapterOld
    ) {
        shareRecords(idToChapterNew);
        removeRecordsAccess(idToChapterNew, idToChapterOld);
    }

    private without sharing class WS_UserSelector {
        private List<User> getUsersByContactId(Set<String> contactIds) {
            return [
                SELECT Id, ContactId, AccountId
                FROM User 
                WHERE ContactId IN :contactIds
                    AND isActive = true
            ];
        }
    }

    private without sharing class WS_ContactSelector {
        private List<Contact> getContacts(Set<String> accountIds, String portalRole) {
            return [
                SELECT Id, AccountId
                FROM Contact 
                WHERE AccountId IN :accountIds 
                    AND PortalRole__c = :portalRole
            ];
        }
    }

    private static void changeCountOfMembershipField(List<ChapterExcellenceProgram__c> chapters) {
        List<ChapterExcellenceProgram__c> chaptersToUpdate = new List<ChapterExcellenceProgram__c>();
        Map<String, Integer> accountIdToStudentCount = new Map<String, Integer>();
        Map<String, String> accountIdToChapterId = new Map<String, String>();

        for (ChapterExcellenceProgram__c chapter : chapters) {
            accountIdToChapterId.put(chapter.NameOfSchool__c, chapter.Id);
        }

        List<Contact> students = new WS_ContactSelector().getContacts(accountIdToChapterId.keySet(), STUDENT_PORTAL_ROLE);

        for (Contact student : students) {
            if (!accountIdToStudentCount.containsKey(student.AccountId)) {
                accountIdToStudentCount.put(student.AccountId, 0);
            }
            accountIdToStudentCount.put(student.AccountId, accountIdToStudentCount.get(student.AccountId) + 1);
        }

        for (String accountId : accountIdToChapterId.keySet()) {
            chaptersToUpdate.add(new ChapterExcellenceProgram__c(
                Id = accountIdToChapterId.get(accountId),
                TotalSkillsUSAMembership__c = accountIdToStudentCount.get(accountId)
            ));
        }

        if (chaptersToUpdate.size() > 0) {
            WS.updateRecords(chaptersToUpdate);
        }
    }

    private static void shareRecords(Map<Id, ChapterExcellenceProgram__c> idToChapterNew) {
        List<ChapterExcellenceProgram__Share> chapterShares = new List<ChapterExcellenceProgram__Share>();
        Map<String, String> accountIdToChapterId = new Map<String, String>();
        Map<String, String> contactIdToUserId = generateContactWithUserMap(idToChapterNew.values());
        Set<String> leadAdvisorsIds = new Set<String>();

        for (ChapterExcellenceProgram__c chapter : idToChapterNew.values()) {
            leadAdvisorsIds.add(chapter.ChapterAdvisor__c);
            accountIdToChapterId.put(chapter.NameOfSchool__c, chapter.Id);

            for (String field : LOOKUP_FIELDS_FOR_SHARE) {
                Id userId = contactIdToUserId.get((String) chapter.get(field));
                if (userId != null) {
                    chapterShares.add(cep_ChapterShareHelper.createShare(chapter.Id, userId));
                }
            }
        }

        List<ChapterExcellenceProgram__Share> leadAdvisorChapterShares = generateSharesForLeadAdvisors(
            accountIdToChapterId,
            leadAdvisorsIds
        );
        chapterShares.addAll(leadAdvisorChapterShares);

        WS.insertRecords(chapterShares);
    }

    private static List<ChapterExcellenceProgram__Share> generateSharesForLeadAdvisors(
        Map<String, String> accountIdToChapterId,
        Set<String> leadAdvisorsIds
    ) {
        List<ChapterExcellenceProgram__Share> chapterShares = new List<ChapterExcellenceProgram__Share>();
        List<Contact> contacts = new WS_ContactSelector().getContacts(accountIdToChapterId.keySet(), LEAD_ADVISOR_PORTAL_ROLE);
        Set<String> contactIds = new Set<String>();
        
        for (Contact contact : contacts) {
            if (!leadAdvisorsIds.contains(contact.Id)) {
                contactIds.add(contact.Id);
            }
        }

        if (contactIds.size() > 0) {
            List<User> secondaryLeadAdvisors = new WS_UserSelector().getUsersByContactId(contactIds);

            for (User leadAdvisor : secondaryLeadAdvisors) {
                Id chapterId = accountIdToChapterId.get(leadAdvisor.AccountId);
                chapterShares.add(cep_ChapterShareHelper.createShare(chapterId, leadAdvisor.Id));
            }
        }

        return chapterShares;
    }

    private static void removeRecordsAccess(
        Map<Id, ChapterExcellenceProgram__c> idToChapterNew,
        Map<Id, ChapterExcellenceProgram__c> idToChapterOld
    ) {
        List<ChapterExcellenceProgram__Share> sharesToDelete = new List<ChapterExcellenceProgram__Share>();
        Map<String, String> contactIdToUserId = generateContactWithUserMap(idToChapterOld.values());
        List<ChapterExcellenceProgram__Share> existingShares = new cep_ChapterShareHelper.WS_ShareSelector()
            .getChapterSharesByParentIds(idToChapterOld.keySet());
 
        for (ChapterExcellenceProgram__Share existingShare : existingShares) {
            ChapterExcellenceProgram__c chapterNew = idToChapterNew.get(existingShare.ParentId);
            ChapterExcellenceProgram__c chapterOld = idToChapterOld.get(existingShare.ParentId);
            
            if (checkInconsistentAccess(contactIdToUserId, existingShare, chapterNew, chapterOld)) {
                sharesToDelete.add(existingShare);
            }
        }
    
        if (!sharesToDelete.isEmpty()) {
            WS.deleteRecords(sharesToDelete);
        }
    }
    
    private static Boolean checkInconsistentAccess(
        Map<String, String> contactIdToUserId,
        ChapterExcellenceProgram__Share existingShare,
        ChapterExcellenceProgram__c chapterNew,
        ChapterExcellenceProgram__c chapterOld
    ) {
        Boolean hasInconsistentAccess = false;
        for (String field : LOOKUP_FIELDS_FOR_SHARE) {
            String fieldValueOld = (String) chapterOld.get(field);
            String fieldValueNew = (String) chapterNew.get(field);

            if (existingShare.UserOrGroupId == contactIdToUserId.get(fieldValueNew)) {
                return false;
            }

            if (fieldValueOld != fieldValueNew && existingShare.UserOrGroupId == contactIdToUserId.get(fieldValueOld)) {
                hasInconsistentAccess = true;
            }      
        }
    
        return hasInconsistentAccess;
    }

    private static Map<String, String> generateContactWithUserMap(List<ChapterExcellenceProgram__c> chapters) {
        Map<String, String> contactWithUserMap = new Map<String, String>();
        List<User> users = getUsersByChapters(chapters);

        for (User user : users) {
            contactWithUserMap.put(user.ContactId, user.Id);
        }

        return contactWithUserMap;
    }

    private static List<User> getUsersByChapters(List<ChapterExcellenceProgram__c> chapters) {
        Set<String> contactIds = new Set<String>();

        for (ChapterExcellenceProgram__c chapter : chapters) {
            for (String field : LOOKUP_FIELDS_FOR_SHARE) {
                if (chapter.get(field) != null) {
                    contactIds.add((String) chapter.get(field));
                }
            }
        }

        return new WS_UserSelector().getUsersByContactId(contactIds);
    }
}
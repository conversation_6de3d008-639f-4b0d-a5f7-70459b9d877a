public with sharing class cep_UploadApplicationImagesCtrl {

    @AuraEnabled(Cacheable=false)
    public static cep_UploadApplicationImagesService.ImagesInfo getAppImagesInfo(String recordId) {
        try {
            return cep_UploadApplicationImagesService.getAppImagesInfo(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void updateAppImage(String recordId, String fileInfo) {
        try {
            cep_UploadApplicationImagesService.updateAndShareAppImage(recordId, fileInfo);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void deleteAppImage(String contentVersionId) {
        try {
            cep_UploadApplicationImagesService.deleteFile(contentVersionId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}
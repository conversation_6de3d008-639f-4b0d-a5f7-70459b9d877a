@IsTest
public class cep_JudgeAssessmentTriggerHandlerTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createJudgeUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createJudgeUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact judgeContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Judge'
            );
            insert judgeContact;
            PortalUser.create(judgeContact);
        }
    }

    @IsTest
    static void shareRecordTest() {
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Id recordId;
        List<ChapterExcellenceProgram__c> chapter;
        System.runAs(cep_TestUtils.getAdmin()) {
            Account schoolAccount = new Account(Name = 'Test school');
            insert schoolAccount;
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = 'Ready for assessment',
                NameOfSchool__c = schoolAccount.Id
            );
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
        }
        Test.startTest();
        System.runAs(judgeUser) {
            chapter = [SELECT Id, NameOfSchool__r.Name FROM ChapterExcellenceProgram__c WHERE Id = :recordId];
        }
        Test.stopTest();
        Assert.areEqual(1, chapter.size(), 'Wrong chapter list size');
        Assert.areEqual('Test school', chapter[0].NameOfSchool__r.Name, 'Wrong chapter name of school');
    }

    @IsTest
    static void removeSharesAfterDoneStatusTest() {
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Id recordId;
        List<ChapterExcellenceProgram__c> chapter;
        List<Account> schoolAccount;
        System.runAs(cep_TestUtils.getAdmin()) {
            Account chapterSchoolAccount = new Account(Name = 'Test school');
            insert chapterSchoolAccount;
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = 'Ready for assessment',
                NameOfSchool__c = chapterSchoolAccount.Id
            );
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
            judge.AssessmentStatus__c = 'Done';
            update judge;
        }
        Test.startTest();
        System.runAs(judgeUser) {
            chapter = [SELECT Id FROM ChapterExcellenceProgram__c WHERE Id = :recordId];
            schoolAccount = [SELECT Id FROM Account WHERE Name = 'Test school'];
        }
        Test.stopTest();
        Assert.areEqual(0, chapter.size(), 'Wrong chapter list size');
        Assert.areEqual(0, schoolAccount.size(), 'Wrong school accounts list size');
    }

    @IsTest
    static void removeSharesTest() {
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Id recordId;
        List<ChapterExcellenceProgram__c> chapter;
        List<Account> schoolAccount;
        System.runAs(cep_TestUtils.getAdmin()) {
            Account chapterSchoolAccount = new Account(Name = 'Test school');
            insert chapterSchoolAccount;
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = 'Ready for assessment',
                NameOfSchool__c = chapterSchoolAccount.Id
            );
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
            delete judge;
        }
        Test.startTest();
        System.runAs(judgeUser) {
            chapter = [SELECT Id FROM ChapterExcellenceProgram__c WHERE Id = :recordId];
            schoolAccount = [SELECT Id FROM Account WHERE Name = 'Test school'];
        }
        Test.stopTest();
        Assert.areEqual(0, chapter.size(), 'Wrong chapter list size');
        Assert.areEqual(0, schoolAccount.size(), 'Wrong school accounts list size');
    }
}
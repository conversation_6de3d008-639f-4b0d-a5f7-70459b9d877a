public with sharing class cep_ApplicationProcessCtrl {
    @AuraEnabled
    public static Boolean checkVisibility() {
        try {
            if (PortalUser.hasRole(PortalUser.Role.LEAD_ADVISOR) && PortalUser.getContactId() != null) {
                cep_ChapterService.DateInfo dateInfo = cep_ChapterService.generateDateInfo(cep_Constant.SUBMIT_DEADLINE);
                return dateInfo.currentDate >= dateInfo.startDate && dateInfo.currentDate < dateInfo.endDate;
            } 
            
            return cep_ChapterService.checkVisibility();
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static cep_ApplicationProcessService.ProcessApplicationInfo createApplicationProcess() {
        try {
            return cep_ApplicationProcessService.createApplicationProcess();
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}
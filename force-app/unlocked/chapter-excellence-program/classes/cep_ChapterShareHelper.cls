public with sharing class cep_ChapterShareHelper {
    public static final String MANUAL_ROW_CAUSE = 'Manual';
    public static final String READ_ACCESS_LEVEL = 'Read';
    public static final String EDIT_ACCESS_LEVEL = 'Edit';
    public static final String NONE_ACCESS_LEVEL = 'None';

    public without sharing class WS_ShareSelector {
        public List<ChapterExcellenceProgram__Share> getChapterSharesByUserIds(List<String> userIds) {
            return [
                SELECT Id, ParentId, UserOrGroupId
                FROM ChapterExcellenceProgram__Share
                WHERE UserOrGroupId IN :userIds AND RowCause = :MANUAL_ROW_CAUSE
            ];
        }

        public List<ChapterExcellenceProgram__Share> getChapterSharesByParentIds(Set<Id> parentIds) {
            return [
                SELECT Id, ParentId, UserOrGroupId
                FROM ChapterExcellenceProgram__Share
                WHERE ParentId IN :parentIds AND RowCause = :MANUAL_ROW_CAUSE
            ];
        }
    }

    public static ChapterExcellenceProgram__Share createShare(Id parentId, Id userId) {
        return new ChapterExcellenceProgram__Share(
            ParentId = parentId,
            UserOrGroupId = userId,
            AccessLevel = EDIT_ACCESS_LEVEL,
            RowCause = MANUAL_ROW_CAUSE
        );
    }
}
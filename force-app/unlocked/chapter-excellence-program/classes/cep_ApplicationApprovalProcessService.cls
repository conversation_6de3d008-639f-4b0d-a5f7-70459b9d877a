public with sharing class cep_ApplicationApprovalProcessService {
    public static final String READY_FOR_MEMBERS_STATUS = 'Ready for members';
    public static final String READY_FOR_ADVISORS_STATUS = 'Ready for advisors review';
    public static final String NEED_CHANGES_FROM_MEMBERS_STATUS = 'Needed changes from members';
    public static final String READY_FOR_ASSESSMENT_STATUS = 'Ready for assessment';
    public static final String DONE_STATUS = 'Done';

    public static ApprovalProcessVisibility getVisibilityInfo(String recordId) {
        ChapterExcellenceProgram__c chapter = getChapterInfo(recordId);
        String status = chapter.Status__c;
        ApprovalProcessVisibility visibilityInfo = new ApprovalProcessVisibility();

        Id contactId = PortalUser.getContactId();

        if (contactId == null) {
            return null;
        }

        if (status == READY_FOR_ADVISORS_STATUS) {
            visibilityInfo.isVisibleApprovalProcess = chapter.ChapterAdvisor__c == contactId;
            visibilityInfo.isVisibleRejectButton = status == READY_FOR_ADVISORS_STATUS;
        } else if (status == READY_FOR_MEMBERS_STATUS || status == NEED_CHANGES_FROM_MEMBERS_STATUS) {
            visibilityInfo.isVisibleApprovalProcess = chapter.ChapterPresident__c == contactId || chapter.ChapterSecretary__c == contactId;
            visibilityInfo.isVisibleRejectButton = false;
        }

        return visibilityInfo;
    }

    public static void changeStatus(String recordId, Boolean isApprove, String comment) {
        ChapterExcellenceProgram__c chapter = getChapterInfo(recordId);
        Map<String, Map<String, String>> statusWithEmailInfo = getStatusWithEmailInfo();
        String nextStatus = getNextStatus(chapter.Status__c, isApprove);

        WS.updateRecord(new ChapterExcellenceProgram__c(Id = chapter.Id, Status__c = nextStatus));

        if (statusWithEmailInfo.containsKey(nextStatus)) {
            List<Messaging.SingleEmailMessage> emails = generateEmail(chapter, nextStatus, comment);

            if (emails != null) {
                Messaging.sendEmail(emails, false);
            }
        }
    }

    private static String getNextStatus(String currentStatus, Boolean isApprove) {
        if (currentStatus == READY_FOR_ADVISORS_STATUS) {
            return isApprove ? READY_FOR_ASSESSMENT_STATUS : NEED_CHANGES_FROM_MEMBERS_STATUS;
        }

        return getStatusTransitions().get(currentStatus);
    }

    private static List<Messaging.SingleEmailMessage> generateEmail(ChapterExcellenceProgram__c chapter, String nextStatus, String comment) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        List<String> toAddresses = generateAddresses(chapter, nextStatus);

        if (toAddresses.isEmpty()) {
            return null;
        }
        
        Map<String, Map<String, String>> statusWithEmailInfo = getStatusWithEmailInfo();
        String orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
        String rejectComment = String.isNotBlank(comment) ? comment : '';

        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        if (String.isNotBlank(orgWideEmailId)) {
            email.setOrgWideEmailAddressId(orgWideEmailId);
        }
        email.setToAddresses(toAddresses);
        email.setSubject(statusWithEmailInfo.get(nextStatus).get('subject'));
        email.setPlainTextBody(statusWithEmailInfo.get(nextStatus).get('body').replace('{comment}', rejectComment));
        emails.add(email);
        
        return emails;
    }

    private static List<String> generateAddresses(ChapterExcellenceProgram__c chapter, String status) {
        List<String> toAddresses = new List<String>();

        if (status == READY_FOR_ADVISORS_STATUS) {
            toAddresses.add(chapter.ChapterAdvisor__r.Email);
            return toAddresses;
        }

        if (chapter.ChapterPresident__r.Email != null) {
            toAddresses.add(chapter.ChapterPresident__r.Email);
        }

        if (chapter.ChapterSecretary__r.Email != null) {
            toAddresses.add(chapter.ChapterSecretary__r.Email);
        }

        return toAddresses;
    }

    private static Map<String, String> getStatusTransitions() {
        return new Map<String, String> {
            READY_FOR_MEMBERS_STATUS => READY_FOR_ADVISORS_STATUS,
            NEED_CHANGES_FROM_MEMBERS_STATUS => READY_FOR_ADVISORS_STATUS,
            READY_FOR_ASSESSMENT_STATUS => DONE_STATUS
        };
    }

    private static Map<String, Map<String, String>> getStatusWithEmailInfo() {
        return new Map<String, Map<String, String>> {
            READY_FOR_ADVISORS_STATUS => new Map<String, String> {
                'subject' => System.Label.cepReadyForAdvisorReviewSubject,
                'body' => System.Label.cepReadyForAdvisorReviewBody
            },
            NEED_CHANGES_FROM_MEMBERS_STATUS => new Map<String, String> {
                'subject' => System.Label.cepNeededChangesFromMembersSubject,
                'body' => System.Label.cepNeededChangesFromMembersBody
            }
        };
    }

    private static ChapterExcellenceProgram__c getChapterInfo(String recordId) {
        return [
            SELECT Id, Status__c, ChapterAdvisor__r.Email, ChapterPresident__r.Email, ChapterSecretary__r.Email
            FROM ChapterExcellenceProgram__c
            WHERE Id = :recordId
        ];
    }

    public class ApprovalProcessVisibility {
        @AuraEnabled
        public Boolean isVisibleApprovalProcess;
        @AuraEnabled
        public Boolean isVisibleRejectButton;
    }
}
@isTest
private class cep_ApplicationApprovalProcessCtrlTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact studentContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Student'
            );
            insert studentContact;
            PortalUser.create(studentContact);
            Contact advisorContact = new Contact(
                FirstName = 'Lead',
                LastName = 'Advisor',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Lead Advisor'
            );
            insert advisorContact;
            PortalUser.create(advisorContact);
        }
    }

    @IsTest
    static void getVisibilityInfoTest() {
        cep_ApplicationApprovalProcessService.ApprovalProcessVisibility visibilityInfo = 
            new cep_ApplicationApprovalProcessService.ApprovalProcessVisibility();
        User leadAdvisor = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(leadAdvisor) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = cep_ApplicationApprovalProcessService.READY_FOR_ADVISORS_STATUS,
                ChapterAdvisor__c = leadAdvisor.ContactId
            );
            insert cep;
            visibilityInfo = cep_ApplicationApprovalProcessCtrl.getVisibilityInfo(cep.Id);
        }
        Test.stopTest();
        Assert.areEqual(true, visibilityInfo.isVisibleApprovalProcess, 'Wrong isVisibleApprovalProcess value');
        Assert.areEqual(true, visibilityInfo.isVisibleRejectButton, 'Wrong isVisibleRejectButton value');
    }

    @IsTest
    static void getVisibilityInfoAsMemberTest() {
        User member = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        cep_ApplicationApprovalProcessService.ApprovalProcessVisibility visibilityInfo = 
            new cep_ApplicationApprovalProcessService.ApprovalProcessVisibility(); 
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = cep_ApplicationApprovalProcessService.READY_FOR_MEMBERS_STATUS,
                ChapterPresident__c = member.ContactId
            );
            insert cep;
        }
        Test.startTest();
        System.runAs(member) {
            Id recordId = [SELECT Id FROM ChapterExcellenceProgram__c LIMIT 1].Id;
            visibilityInfo = cep_ApplicationApprovalProcessCtrl.getVisibilityInfo(recordId);
        }
        Test.stopTest();
        Assert.areEqual(true, visibilityInfo.isVisibleApprovalProcess, 'Wrong isVisibleApprovalProcess value');
        Assert.areEqual(false, visibilityInfo.isVisibleRejectButton, 'Wrong isVisibleRejectButton value');
    }

    @IsTest
    static void getVisibilityInfoErrorTest() {
        Boolean isError = false;
        cep_ApplicationApprovalProcessService.ApprovalProcessVisibility visibilityInfo = 
            new cep_ApplicationApprovalProcessService.ApprovalProcessVisibility();
        Test.startTest();
        try {
            visibilityInfo = cep_ApplicationApprovalProcessCtrl.getVisibilityInfo(null);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }

    @IsTest
    static void changeChapterStatusTest() {
        ChapterExcellenceProgram__c cepAfter;
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = cep_ApplicationApprovalProcessService.READY_FOR_ADVISORS_STATUS
            );
            insert cep;
            cep_ApplicationApprovalProcessCtrl.changeChapterStatus(cep.Id, true, null);
            cepAfter = [SELECT Status__c FROM ChapterExcellenceProgram__c LIMIT 1];
        }
        Test.stopTest();
        Assert.areEqual(cep_ApplicationApprovalProcessService.READY_FOR_ASSESSMENT_STATUS, cepAfter.Status__c, 'Wrong chapter status value');
    }

    @IsTest
    static void changeChapterStatusAsMemberTest() {
        User member = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        ChapterExcellenceProgram__c cepAfter;
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = cep_ApplicationApprovalProcessService.READY_FOR_MEMBERS_STATUS,
                ChapterPresident__c = member.ContactId
            );
            insert cep;
        }
        Test.startTest();
        System.runAs(member) {
            Id recordId = [SELECT Id FROM ChapterExcellenceProgram__c LIMIT 1].Id;
            cep_ApplicationApprovalProcessCtrl.changeChapterStatus(recordId, true, null);
        }
        Test.stopTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            cepAfter = [SELECT Status__c FROM ChapterExcellenceProgram__c LIMIT 1];
        }
        Assert.areEqual(cep_ApplicationApprovalProcessService.READY_FOR_ADVISORS_STATUS, cepAfter.Status__c, 'Wrong chapter status value');
    }

    @IsTest
    static void changeChapterStatusErrorTest() {
        Boolean isError = false;
        Test.startTest();
        try {
            cep_ApplicationApprovalProcessCtrl.changeChapterStatus(null, true, null);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }
}
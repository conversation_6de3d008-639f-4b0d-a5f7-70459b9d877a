<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Send notification to sys admins (CEP ready for assessment)</description>
        <name>Send_notification_to_sys_admins</name>
        <label>Send notification to sys admins</label>
        <locationX>925</locationX>
        <locationY>872</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Get_Notification_Type.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>RecipientIds</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>$Label.cepReadyForAssessmentNotificationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>CustomNotificationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <assignments>
        <description>Add User Id to Collection</description>
        <name>Add_User_Id_to_Collection</name>
        <label>Add User Id to Collection</label>
        <locationX>1102</locationX>
        <locationY>678</locationY>
        <assignmentItems>
            <assignToReference>RecipientIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_System_Administrator_Users.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_System_Administrator_Users</targetReference>
        </connector>
    </assignments>
    <description>Flow for send bell notification to system administrators when CEP status is Ready for assessment</description>
    <environments>Default</environments>
    <formulas>
        <description>CustomNotificationBody</description>
        <name>CustomNotificationBody</name>
        <dataType>String</dataType>
        <expression>&apos;The CEP form from the School &apos; + {!$Record.NameOfSchool__r.Name} + &apos; was submitted, please review it and assign a judge.&apos;</expression>
    </formulas>
    <interviewLabel>[CEP] Notify judges {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[CEP] Notify judges</label>
    <loops>
        <description>Loop System Administrator Users</description>
        <name>Loop_System_Administrator_Users</name>
        <label>Loop System Administrator Users</label>
        <locationX>919</locationX>
        <locationY>678</locationY>
        <collectionReference>Get_System_Administrator_Users</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_User_Id_to_Collection</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Send_notification_to_sys_admins</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get Notification Type</description>
        <name>Get_Notification_Type</name>
        <label>Get Notification Type</label>
        <locationX>777</locationX>
        <locationY>383</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_System_Administrator_Profile</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>CEP_ReadyForAssessment</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get System Administrator Profile</description>
        <name>Get_System_Administrator_Profile</name>
        <label>Get System Administrator Profile</label>
        <locationX>778</locationX>
        <locationY>524</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_System_Administrator_Users</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>System Administrator</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Profile</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get System Administrator Users</description>
        <name>Get_System_Administrator_Users</name>
        <label>Get System Administrator Users</label>
        <locationX>779</locationX>
        <locationY>678</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_System_Administrator_Users</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProfileId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_System_Administrator_Profile.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>650</locationX>
        <locationY>48</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Ready for assessment</stringValue>
            </value>
        </filters>
        <object>ChapterExcellenceProgram__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Get_Notification_Type</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <description>RecipientIds</description>
        <name>RecipientIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>

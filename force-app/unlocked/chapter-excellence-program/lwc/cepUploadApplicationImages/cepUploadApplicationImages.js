import { LightningElement, api, track } from 'lwc';
import { showErrors } from 'c/errorHandler';

import getAppImagesInfo from '@salesforce/apex/cep_UploadApplicationImagesCtrl.getAppImagesInfo';
import updateAppImage from '@salesforce/apex/cep_UploadApplicationImagesCtrl.updateAppImage';
import deleteAppImage from '@salesforce/apex/cep_UploadApplicationImagesCtrl.deleteAppImage';

import labels from './labels';

export default class CepUploadApplicationImages extends LightningElement {
    @api recordId;
    @track activityPhotoInfo;

    personalSkillInfo;
    technicalSkillInfo;
    workplaceSkillInfo;
    
    isLoading = false;

    labels = labels;

    connectedCallback() {
        this.getActivityPhotoInfo();
    }

    getActivityPhotoInfo() {
        this.isLoading = true;
        getAppImagesInfo({ recordId: this.recordId })
        .then((result) => {
            this.activityPhotoInfo = result;
            if (this.activityPhotoInfo && this.activityPhotoInfo.skillNameToActivityPhoto) {
                this.setSkillsInfo();
            }
            this.sendFinishLoadEvent();
        })
        .catch((error) => {
            showErrors(this, error);
        })
        .finally(() => {
            this.isLoading = false;
        });
    }

    setSkillsInfo() {
        this.personalSkillInfo = this.activityPhotoInfo.skillNameToActivityPhoto[this.labels.cepPersonalSkillActivityPhoto];
        this.workplaceSkillInfo = this.activityPhotoInfo.skillNameToActivityPhoto[this.labels.cepWorkplaceSkillActivityPhoto];
        this.technicalSkillInfo = this.activityPhotoInfo.skillNameToActivityPhoto[this.labels.cepTechnicalSkillActivityPhoto];
    }

    deleteLogoImageId(event) {
        this.isLoading = true;
        deleteAppImage({
            contentVersionId: event.detail.contentVersionId
        })
        .catch((error) => {
            showErrors(this, error);
        })
        .finally(() => {
            this.isLoading = false;
        });
    }

    updateAppImageId(documentId, skillTitle, description) {
        this.isLoading = true;
        updateAppImage({
            recordId: this.recordId,
            fileInfo: JSON.stringify({
                contentDocumentId: documentId,
                skillTitle: skillTitle,
                description: description
            })
        })
        .catch((error) => {
            showErrors(this, error);
        })
        .finally(() => {
            this.isLoading = false;
        });
    }

    getUploadedImage(event) {
        const eventDetails = event.detail;
        this.updateAppImageId(
            eventDetails.documentId,
            eventDetails.skillTitle,
            eventDetails.description
        );
    }

    sendFinishLoadEvent() {
        const event = new CustomEvent("imagecontainerready");
        this.dispatchEvent(event);
    }
}
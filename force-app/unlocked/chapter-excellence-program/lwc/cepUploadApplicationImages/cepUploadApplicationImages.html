<template>
    <div if:true={activityPhotoInfo} class="upload-activity-photo-container">
        <c-cep-upload-application-images-item
            skill-name={labels.cepPersonalSkillActivityPhoto}
            image-info={personalSkillInfo}
            record-id={recordId}
            has-edit-permissions={activityPhotoInfo.hasEditPermission}
            onfinishload={getUploadedImage}
            ondeleteimage={deleteLogoImageId}>
        </c-cep-upload-application-images-item>

        <c-cep-upload-application-images-item
            skill-name={labels.cepWorkplaceSkillActivityPhoto}
            image-info={workplaceSkillInfo}
            record-id={recordId}
            has-edit-permissions={activityPhotoInfo.hasEditPermission}
            onfinishload={getUploadedImage}
            ondeleteimage={deleteLogoImageId}>
        </c-cep-upload-application-images-item>

        <c-cep-upload-application-images-item
            skill-name={labels.cepTechnicalSkillActivityPhoto}
            image-info={technicalSkillInfo}
            record-id={recordId}
            has-edit-permissions={activityPhotoInfo.hasEditPermission}
            onfinishload={getUploadedImage}
            ondeleteimage={deleteLogoImageId}>
        </c-cep-upload-application-images-item>
    </div>
</template>
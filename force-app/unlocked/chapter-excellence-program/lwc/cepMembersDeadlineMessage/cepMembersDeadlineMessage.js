import { LightningElement } from 'lwc';
import { ShowToastEvent } from "lightning/platformShowToastEvent";

import getDeadlineDays from '@salesforce/apex/cep_MembersDeadlineMessageCtrl.getDeadlineDays';
import cepMembersDeadlineMessageTitle from '@salesforce/label/c.cepMembersDeadlineMessageTitle';
import cepMembersDeadlineMessage from '@salesforce/label/c.cepMembersDeadlineMessage';

const ONE_HOUR_IN_SECONDS = 3600;
const LOCAL_STORAGE_ITEM_NAME = 'deadlineInfo';

export default class cep_DeadlineToast extends LightningElement {
    deadlineDays;
    labels = {
        cepMembersDeadlineMessageTitle,
        cepMembersDeadlineMessage
    }

    async connectedCallback() {
        const storedDeadlineInfo = JSON.parse(localStorage.getItem(LOCAL_STORAGE_ITEM_NAME));
        const shouldRefresh = this.shouldRefreshDeadline(storedDeadlineInfo);

        if (!storedDeadlineInfo || shouldRefresh) {
            await this.getDeadlineInfo();
            this.setDeadlineInfoToLocalStorage();
    
            if (this.deadlineDays != null && this.deadlineDays > 0) {
                this.showToast();
            }
        }
    }

    shouldRefreshDeadline(storedDeadlineInfo) {
        if (storedDeadlineInfo) {
            const secondsDiff = (new Date() - new Date(storedDeadlineInfo.lastRefreshDate)) / 1000;
            return secondsDiff >= ONE_HOUR_IN_SECONDS && storedDeadlineInfo.deadlineDays != null;
        }
    
        return false;
    }

    async getDeadlineInfo() {
        const data = await getDeadlineDays();
        this.deadlineDays = data;
    }

    async setDeadlineInfoToLocalStorage() {
        localStorage.setItem(
            LOCAL_STORAGE_ITEM_NAME,
            JSON.stringify({
                deadlineDays : this.deadlineDays,
                lastRefreshDate : new Date()
            })
        );
    }

    showToast() {
        const evt = new ShowToastEvent({
            title: this.labels.cepMembersDeadlineMessageTitle,
            message: this.labels.cepMembersDeadlineMessage.replace('{deadlineDays}', this.deadlineDays),
            variant: 'info',
            mode: 'sticky'
        });
        this.dispatchEvent(evt);   
    }
}
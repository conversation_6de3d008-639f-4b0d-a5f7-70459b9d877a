import { LightningElement, api } from 'lwc';
import { NavigationMixin } from "lightning/navigation";
import checkVisibility from '@salesforce/apex/cep_JudgeAssessmentButtonCtrl.checkVisibility';
import cepDoAssessmentButtonLabel from '@salesforce/label/c.cepDoAssessmentButtonLabel'; 

export default class CepJudgeAssessmentButton extends NavigationMixin(LightningElement) {
    @api recordId;
    @api assessmentPageName;
    isVisible;

    labels = {
        cepDoAssessmentButtonLabel
    }

    connectedCallback() {
        this.checkButtonVisibility();
    }

    async checkButtonVisibility() {
        this.isVisible = await checkVisibility({ recordId: this.recordId });
    }

    navigateToAssessmentsPage() {
        this[NavigationMixin.GenerateUrl]({
            type: 'comm__namedPage',
            attributes: {
                name: this.assessmentPageName
            },
            state: {
                chapterExcellenceProgramId: this.recordId 
            }
        }).then(url => {
            window.open(url, '_blank');
        });
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <isExposed>true</isExposed>
    <description>[CEP] Judge Assessment Button</description>
    <masterLabel>[CEP] Judge Assessment Button</masterLabel>
    <targets>
        <target>lightningCommunity__Default</target>
        <target>lightningCommunity__Page</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightningCommunity__Default">
            <property
                name="recordId"
                type="String"
                label="Record Id"
                description="Automatically bind the page's record id to the component variable"
                default="{!recordId}"
            />
            <property
                name="assessmentPageName"
                type="String"
                label="Assessments page api name"
                description="Assessment page api name used for navigation after click button"
            />
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>

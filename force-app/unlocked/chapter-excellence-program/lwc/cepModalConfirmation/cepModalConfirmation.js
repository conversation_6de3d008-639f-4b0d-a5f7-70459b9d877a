import { LightningElement } from 'lwc';

import cepCancelModalButtonLabel from '@salesforce/label/c.cepCancelModalButtonLabel';
import cepConfirmModalButtonLabel from '@salesforce/label/c.cepConfirmModalButtonLabel';

export default class CepModalConfirmation extends LightningElement {
    labels = {
        cepCancelModalButtonLabel,
        cepConfirmModalButtonLabel
    };

    handleClickCancel() {
        this.sendModalStatusCustomEvent(false);
    }

    handleClickOk() {
        this.sendModalStatusCustomEvent(true);
    }

    sendModalStatusCustomEvent(isConfirmed) {
        const event = new CustomEvent('modalstatuschange', {
            detail: {
                isConfirmed: isConfirmed
            }
        });
        this.dispatchEvent(event);
    }
}

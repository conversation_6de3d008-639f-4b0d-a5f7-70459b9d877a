<template>
    <section
        role="dialog"
        tabindex="-1"
        aria-labelledby="modal-heading"
        aria-modal="true"
        aria-describedby="modal-content"
        class="slds-modal modal slds-fade-in-open"
    >
        <div class="slds-modal__container">
            <button
                class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                title={labels.cepCancelModalButtonLabel}
                onclick={handleClickCancel}
            >
                <lightning-icon
                    icon-name="utility:close"
                    alternative-text={labels.cepCancelModalButtonLabel}
                    variant="inverse"
                    size="small"
                ></lightning-icon>
                <span class="slds-assistive-text">{labels.cepCancelModalButtonLabel}</span>
            </button>
            <div class="slds-modal__content slds-p-around_medium" id="modal-content">
                <slot name="body"></slot>
            </div>
            <footer class="slds-modal__footer">
                <lightning-button
                    class="slds-p-left_small"
                    variant="neutral"
                    label={labels.cepCancelModalButtonLabel}
                    title={labels.cepCancelModalButtonLabel}
                    onclick={handleClickCancel}
                ></lightning-button>
                <lightning-button
                    class="slds-p-left_small"
                    variant="brand"
                    label={labels.cepConfirmModalButtonLabel}
                    title={labels.cepConfirmModalButtonLabel}
                    onclick={handleClickOk}
                ></lightning-button>
            </footer>
        </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
</template>

import { LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import checkVisibility from '@salesforce/apex/cep_JudgeAssessmentsCtrl.checkVisibility';

const CHAPTER_ID_URL_PARAM = 'chapterExcellenceProgramId';

export default class cep_JudgeAssessment extends NavigationMixin(LightningElement) {
    chapterId;
    isVisible;

    connectedCallback() {
        const urlParams = new URL(window.location.href).searchParams;

        if (urlParams.has(CHAPTER_ID_URL_PARAM)) {
            this.chapterId = urlParams.get(CHAPTER_ID_URL_PARAM);
            this.checkButtonVisibility();
        }
    }

    async checkButtonVisibility() {
        this.isVisible = await checkVisibility({ recordId: this.chapterId });
    }
}

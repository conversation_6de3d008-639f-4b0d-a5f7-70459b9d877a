import { LightningElement, api, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { showErrors } from 'c/errorHandler';

import labels from './labels';
import getVisibilityInfo from '@salesforce/apex/cep_ApplicationApprovalProcessCtrl.getVisibilityInfo';
import changeChapterStatus from '@salesforce/apex/cep_ApplicationApprovalProcessCtrl.changeChapterStatus';

export default class cepApplicationApprovalProcess extends LightningElement {
    @api recordId;
    @track visibilityInfo;

    confirmationMessage;
    isOpenConfirmationModal = false;
    isApprove = false;
    isLoading = true;
    labels = labels;

    async connectedCallback() {
        await this.getVisibility();
        this.isLoading = false;
    }

    async getVisibility() {
        this.isLoading = true;

        try {
            const data = await getVisibilityInfo({ recordId: this.recordId });
            this.visibilityInfo = data;
        } catch (error) {
            showErrors(this, error);
        }
    }

    handleClickApprove() {
        this.isApprove = true;
        this.confirmationMessage = this.visibilityInfo.isVisibleRejectButton
            ? this.labels.cepStudentApproveConfirmationMsg
            : this.labels.cepLeadAdvisorApproveConfirmationMsg;
        this.isOpenConfirmationModal = true;
    }

    handleClickDecline() {
        if (this.validateRejectSection()) {
            this.isApprove = false;
            this.confirmationMessage = this.labels.cepLeadAdvisorRejectConfirmationMsg;
            this.isOpenConfirmationModal = true;
        }
    }

    async handleChangeModalStatus(event) {
        if (event.detail.isConfirmed) {
            this.processChangeStatus();
        }

        this.isOpenConfirmationModal = false;
    }

    processChangeStatus() {
        if (this.isApprove) {
            this.approveChapter();
        } else {
            this.declineChapter();
        }
    }

    async changeStatus(isApprove, comment) {
        this.isLoading = true;

        try {
            await changeChapterStatus({
                recordId: this.recordId,
                isApprove: isApprove,
                comment: comment
            });
            await this.getVisibility();
            await this.showSuccessToast();
            this.passChangedStatusEvent();
        } catch (error) {
            showErrors(this, error);
        }
    }

    async approveChapter() {
        await this.changeStatus(true, null);
        this.isLoading = false;
    }

    async declineChapter() {
        const commentInput = this.template.querySelector('.reject-comment-input');
        await this.changeStatus(false, commentInput.value);
        this.isLoading = false;
    }

    validateRejectSection() {
        const commentInput = this.template.querySelector('.reject-comment-input');

        if (!commentInput.value) {
            commentInput.setCustomValidity(this.labels.cepApprovalProcessRejectCustomValidity);
        } else {
            commentInput.setCustomValidity('');
        }

        return commentInput.reportValidity();
    }

    passChangedStatusEvent() {
        const event = new CustomEvent('changestatus', { detail: { isChanged: true } });
        this.dispatchEvent(event);
    }

    async showSuccessToast() {
        const evt = new ShowToastEvent({
            title: this.labels.cepApprovalProcessSuccessToastTitle,
            message: this.labels.cepApprovalProcessSuccessToastMessage,
            variant: 'success'
        });
        this.dispatchEvent(evt);
    }

    get isVisibleComponent() {
        return this.visibilityInfo != null && this.visibilityInfo.isVisibleApprovalProcess === true;
    }
}

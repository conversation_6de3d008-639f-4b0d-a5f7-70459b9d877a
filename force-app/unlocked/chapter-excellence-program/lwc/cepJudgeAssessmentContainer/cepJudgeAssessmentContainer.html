<template>
    <template for:each={comboboxes} for:item="combobox" for:index="index">
        <div class="judge-assessment-container" key={combobox.index}>
            <div class="assessment-combobox">
                <span class="assessment-weigth-area">{combobox.weightArea}</span>
    
                <lightning-combobox
                    data-name={combobox.assessmentFieldName}
                    data-weight-area={combobox.weightArea}
                    options={combobox.options}
                    placeholder=""
                    onchange={handleChangeNumber}
                    required
                ></lightning-combobox>
    
                <span class="assessment-total-point" data-name={combobox.assessmentFieldName}>{defaultTotalPoints}</span>
            </div>
        </div>
    </template>
</template>
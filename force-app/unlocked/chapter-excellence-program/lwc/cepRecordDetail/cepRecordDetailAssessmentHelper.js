export function validateAssessments(assessments) {
    let isValid = true;
    assessments.forEach(assessmentItem => {
        if (!assessmentItem.checkValidation()) {
            isValid = false;
            return;
        }
    });

    return isValid;
}

export function generateImageAssessments(cmp) {
    let imageAssessments = [];
    const imagePrefix = 'Image';
    const imageDescriptionPrefix = 'ImageDesc';

    const imageAssessmentsAliases = [
        cmp.labels.cepPersonalSkillActivityPhoto,
        cmp.labels.cepWorkplaceSkillActivityPhoto,
        cmp.labels.cepTechnicalSkillActivityPhoto
    ];

    imageAssessmentsAliases.forEach((field, index) => {
        imageAssessments.push({
            inputName: field,
            fieldCategory: imagePrefix,
            skillIndex: index,
            comboboxes: [
                {
                    assessmentFieldName: 'PhotoOrCaption__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 2
                }
            ]
        },
        {
            inputName: field,
            fieldCategory: imageDescriptionPrefix,
            skillIndex: index,
            comboboxes: [
                {
                    assessmentFieldName: 'SpellingGrammar__c',
                    options: generateAssessmentOptions(1, 4),
                    weightArea: 1
                }
            ]
        });
    });

    return imageAssessments;
}

export function generateFormAssessments() {
    let formAssessments = [];

    const essentialElementGoalFields = [
        'WhatWasTheEssentialElement1__c',
        'WhatWasTheEssentialElement2__c',
        'WhatWasTheEssentialElement3__c'
    ];

    essentialElementGoalFields.forEach((field, index) => {
        formAssessments.push({
            inputName: field,
            skillIndex: index,
            comboboxes: [
                {
                    assessmentFieldName: 'FrameworkEssentialFWEE__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 2
                }
            ]
        });
    });

    const threeGoalsActivityFields = [
        'WhatWereThreeGoalsOfTheActivity1__c',
        'WhatWereThreeGoalsOfTheActivity2__c',
        'WhatWereThreeGoalsOfTheActivity3__c'
    ];

    threeGoalsActivityFields.forEach((field, index) => {
        formAssessments.push({
            inputName: field,
            skillIndex: index,
            comboboxes: [
                {
                    assessmentFieldName: 'FrameworkEssentialGoalNumberOne__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 1
                },
                {
                    assessmentFieldName: 'FrameworkEssentialGoalNumberTwo__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 1
                },
                {
                    assessmentFieldName: 'FrameworkEssentialGoalNumberThree__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 1
                }
            ]
        });
    });

    const planOfActionFields = [
        'PlanOfAction1__c',
        'PlanOfAction2__c',
        'PlanOfAction3__c'
    ];

    planOfActionFields.forEach((field, index) => {
        formAssessments.push({
            inputName: field,
            skillIndex: index,
            comboboxes: [
                {
                    assessmentFieldName: 'PlanOfAction__c',
                    options: generateAssessmentOptions(1, 10),
                    weightArea: 3
                }
            ]
        });
    });

    const frameworkEssentialOutcomeFields = [
        'FrameworkEssentialElementOutcome1__c',
        'FrameworkEssentialElementOutcome2__c',
        'FrameworkEssentialElementOutcome3__c'
    ];

    frameworkEssentialOutcomeFields.forEach((field, index) => {
        formAssessments.push({
            inputName: field,
            skillIndex: index,
            comboboxes: [
                {
                    assessmentFieldName: 'WhatWasAccomplished__c',
                    options: generateAssessmentOptions(1, 15),
                    weightArea: 1
                },
                {
                    assessmentFieldName: 'RelatedToFrameworkAndEssential__c',
                    options: generateAssessmentOptions(1, 15),
                    weightArea: 1
                }
            ]
        });
    });

    const frameworkEssentialGoalsFields = [
        'FrameworkEssentialElementGoals1__c',
        'FrameworkEssentialElementGoals2__c',
        'FrameworkEssentialElementGoals3__c'
    ];

    frameworkEssentialGoalsFields.forEach((field, index) => {
        formAssessments.push({
            inputName: field,
            skillIndex: index,
            comboboxes: [
                {
                    assessmentFieldName: 'FWEEGoal__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 2
                },
                {
                    assessmentFieldName: 'GoalNumberOne__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 1
                },
                {
                    assessmentFieldName: 'GoalNumberTwo__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 1
                },
                {
                    assessmentFieldName: 'GoalNumberThree__c',
                    options: generateAssessmentOptions(1, 3),
                    weightArea: 1
                }
            ]
        });
    });

    return formAssessments;
}

function generateAssessmentOptions(min, max) {
    let options = [];
    for (let i = min; i <= max; i++) {
        options.push({ value: i.toString(), label: i.toString() });
    }

    return options;
}
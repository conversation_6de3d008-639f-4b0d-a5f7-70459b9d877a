import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';

export function showSuccessToast(cmp, message) {
    const event = new ShowToastEvent({
        title: 'Success!',
        message: message,
        variant: 'success'
    });
    cmp.dispatchEvent(event);
}

export function navigateToHomePage(cmp) {
    cmp[NavigationMixin.Navigate]({
        type: 'comm__namedPage',
        attributes: {
            name: 'Home'
        }
    });
}

export function overrideFormFields(cmp) {
    return [
        { name: "AllSectionProgramAdvisorsAreSubmitted__c", label: cmp.labels.cepAllSectionProgramAdvisorsAreSubmittedLabel },
        { name: "TheChapterConductedWellPlanned__c", label: cmp.labels.cepTheChapterConductedWellPlannedLabel },
        { name: "TheChapterCompletedAProjectedBudget__c", label: cmp.labels.cepTheChapterCompletedAProjectedBudgetLabel },
        { name: "TheChapterCompletedAProgramOfWork__c", label: cmp.labels.cepTheChapterCompletedAProgramOfWorkLabel },
        { name: "PersonalSkill__c", label: cmp.labels.cepPersonalSkillLabel },
        { name: "WorkplaceSkill__c", label: cmp.labels.cepWorkplaceSkillLabel },
        { name: "TechnicalSkill__c", label: cmp.labels.cepTechnicalSkillLabel },
        { name: "CelebratedSkillsUSAWeek__c", label: cmp.labels.cepCelebratedSkillsUSAWeekLabel },
        { name: "ChapterAwardsProgramIs__c", label: cmp.labels.cepChapterAwardsProgramIsLabel },
        { name: "ChapterMembersAttendedOneActivity__c", label: cmp.labels.cepChapterMembersAttendedOneActivityLabel },
        { name: "ConductedActivityToEngageBusiness__c", label: cmp.labels.cepConductedActivityToEngageBusinessLabel },
        { name: "ConductedChapterRecruitmentActivity__c", label: cmp.labels.cepConductedChapterRecruitmentActivityLabel },
        { name: "FrameworkEssentialElementGoals1__c", label: cmp.labels.cepFrameworkEssentialElementGoals1Label },
        { name: "FrameworkEssentialElementGoals2__c", label: cmp.labels.cepFrameworkEssentialElementGoals2Label },
        { name: "FrameworkEssentialElementGoals3__c", label: cmp.labels.cepFrameworkEssentialElementGoals3Label },
        { name: "FrameworkEssentialElementOutcome1__c", label: cmp.labels.cepFrameworkEssentialElementOutcome1Label },
        { name: "FrameworkEssentialElementOutcome2__c", label: cmp.labels.cepFrameworkEssentialElementOutcome2Label },
        { name: "FrameworkEssentialElementOutcome3__c", label: cmp.labels.cepFrameworkEssentialElementOutcome3Label },
        { name: "HeldExecutiveCommitteeMeetings__c", label: cmp.labels.cepHeldExecutiveCommitteeMeetingsLabel },
        { name: "HeldSkillsUSALocalLeadershipArea__c", label: cmp.labels.cepHeldSkillsUSALocalLeadershipAreaLabel },
        { name: "HeldSkillsUSALocalTechnicalArea__c", label: cmp.labels.cepHeldSkillsUSALocalTechnicalAreaLabel },
        { name: "LocalChapterHasSocialMedia__c", label: cmp.labels.cepLocalChapterHasSocialMediaLabel },
        { name: "MembersAreEngaged__c", label: cmp.labels.cepMembersAreEngagedLabel },
        { name: "OneOrMoreArticlesWerePublished__c", label: cmp.labels.cepOneOrMoreArticlesWerePublishedLabel },
        { name: "ParticipateInCareerEssentials__c", label: cmp.labels.cepParticipateInCareerEssentialsLabel },
        { name: "PlanToParticipateInSkillsUSASigning__c", label: cmp.labels.cepPlanToParticipateInSkillsUSASigningLabel },
        { name: "PlanToparticipateInStateLeadership__c", label: cmp.labels.cepPlanToparticipateInStateLeadershipLabel },
        { name: "ReportChapterActivities__c", label: cmp.labels.cepReportChapterActivitiesLabel },
        { name: "StudentsAreSkillsUSAMembers__c", label: cmp.labels.cepStudentsAreSkillsUSAMembersLabel },
        { name: "StudentsAttendedFallLeadership__c", label: cmp.labels.cepStudentsAttendedFallLeadershipLabel },
        { name: "WhichFrameworkElementsApply3__c", label: cmp.labels.cepWhichFrameworkElementsApply3Label },
        { name: "WhichFrameworkElementsApply2__c", label: cmp.labels.cepWhichFrameworkElementsApply2Label },
        { name: "WhichFrameworkElementsApply1__c", label: cmp.labels.cepWhichFrameworkElementsApply1Label },
        { name: "WhatWereThreeGoalsOfTheActivity3__c", label: cmp.labels.cepWhatWereThreeGoalsOfTheActivity3Label },
        { name: "WhatWereThreeGoalsOfTheActivity2__c", label: cmp.labels.cepWhatWereThreeGoalsOfTheActivity2Label },
        { name: "WhatWereThreeGoalsOfTheActivity1__c", label: cmp.labels.cepWhatWereThreeGoalsOfTheActivity1Label },
        { name: "WhatWasTheEssentialElement3__c", label: cmp.labels.cepWhatWasTheEssentialElement3Label },
        { name: "WhatWasTheEssentialElement2__c", label: cmp.labels.cepWhatWasTheEssentialElement2Label },
        { name: "WhatWasTheEssentialElement1__c", label: cmp.labels.cepWhatWasTheEssentialElement1Label }
    ];
}
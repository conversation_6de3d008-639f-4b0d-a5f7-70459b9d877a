<template lwc:render-mode="light">
    <lightning-spinner if:true={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>

    <div class="slds-p-horizontal_xx-small slds-m-bottom_xx-small" if:true={isNotExpiredDate}>
        <div class="slds-text-heading_medium">{labels.cepChapterInformationSectionLabel}</div>

        <div class="slds-p-bottom_small">
            <c-cep-record-detail-main-information chapter-info={chapterInfo}></c-cep-record-detail-main-information>

            <aclab-form
                record-id={recordId}
                get-form-method={formSectionsRetrieveMethod}
                mode={chapterInfo.formMode}
                overrides={overrides}
                show-section-label
                respect-field-level-security
                onformsubmit={handleClickSubmitForm}
                onformready={formIsReady}
            ></aclab-form>
        </div>

        <div class="slds-p-bottom_small">
            <c-cep-application-approval-process
                record-id={recordId}
                onchangestatus={handleChangeStatus}
            ></c-cep-application-approval-process>

            <c-cep-upload-application-images
                record-id={recordId}
                onimagecontainerready={imageContainerIsReady}
            ></c-cep-upload-application-images>
        </div>

        <c-cep-modal-confirmation if:true={isOpenConfirmationModal} onmodalstatuschange={handleChangeModalStatus}>
            <h3 slot="body">{confirmationMessage}</h3>
        </c-cep-modal-confirmation>

        <div if:true={isJudge} class="slds-grid slds-wrap slds-p-bottom_small">
            <div class="slds-size_1-of-2">
                <lightning-button
                    if:true={showAssessmentSaveButton}
                    label={labels.cepJudgeAssessmentSubmitButtonLabel}
                    onclick={saveAssessments}
                ></lightning-button>
            </div>

            <c-cep-assessment-stash
                assessment-form-info={assessmentFormInfo}
                assessment-image-info={assessmentImageInfo}
                onchangetotalpoints={recalculateTotalPoints}
            ></c-cep-assessment-stash>
        </div>
    </div>
</template>

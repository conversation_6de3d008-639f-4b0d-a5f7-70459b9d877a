import { LightningElement, api } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { showErrors } from 'c/errorHandler';

import { overrideFormFields, showSuccessToast, navigateToHomePage } from './cepRecordDetailHelper';
import {
    generateFormAssessments,
    generateImageAssessments,
    validateAssessments
} from './cepRecordDetailAssessmentHelper';

import checkVisibility from '@salesforce/apex/cep_RecordDetailCtrl.checkVisibility';
import checkJudgeVisibility from '@salesforce/apex/cep_RecordDetailCtrl.checkJudgeVisibility';
import getChapterInfo from '@salesforce/apex/cep_RecordDetailCtrl.getChapterInfo';
import getFormSections from '@salesforce/apex/cep_RecordDetailCtrl.getFormSections';
import updateChapter from '@salesforce/apex/cep_RecordDetailCtrl.updateChapter';
import saveAssessments from '@salesforce/apex/cep_RecordDetailCtrl.saveAssessments';

import labels from './labels';

const SKILL_INDEX_TO_SECTION = {
    0: 'section-3',
    1: 'section-4',
    2: 'section-5'
};

export default class cep_RecordDetail extends NavigationMixin(LightningElement) {
    static renderMode = 'light';
    @api recordId;

    isVisible;
    isJudge;
    isLoading = true;
    isOpenConfirmationModal = false;
    skipSubmitConfirmation = false;
    confirmationMessage;
    overrides;
    chapterRecord;
    chapterInfo;

    assessmentFormInfo;
    assessmentImageInfo;
    assessmentRecords = [];
    showAssessmentSaveButton = false;
    totalPoints = 0;

    labels = labels;

    formSectionsRetrieveMethod;

    get isNotExpiredDate() {
        return this.chapterInfo && !this.chapterInfo.isExpired;
    }

    async connectedCallback() {
        try {
            this.formSectionsRetrieveMethod = () => getFormSections({ recordId: this.recordId });
            await this.checkVisibility();
            await this.checkJudgeVisibility();
            await this.getChapterInformation();
            this.setUpFormOverrides();

            this.isLoading = false;
        } catch (error) {
            console.error(error);
        }
    }

    async imageContainerIsReady() {
        if (this.isJudge) {
            await this.generateImageAssessments();
            this.displayImageAssessments();
        }
    }

    async formIsReady() {
        this.overrideLookupFields();

        if (this.isJudge) {
            await this.generateFormAssessments();
            this.displayFormAssessments();
            this.displaySkillsTotalPointsSections();
            this.displayAssessmentsFieldNames();
        }
    }

    async checkVisibility() {
        this.isLoading = true;
        try {
            this.isVisible = await checkVisibility({ recordId: this.recordId });
        } catch (error) {
            console.error();
        }
    }

    async checkJudgeVisibility() {
        this.isJudge = await checkJudgeVisibility({ recordId: this.recordId });

        if (this.isJudge) {
            this.showAssessmentSaveButton = true;
        }
    }

    async getChapterInformation() {
        try {
            const data = await getChapterInfo({ recordId: this.recordId });
            this.chapterInfo = data;

            if (this.chapterInfo.isExpired) {
                showErrors(this, this.chapterInfo.errorMessage);
                navigateToHomePage(this);
            }
        } catch (error) {
            console.error(error);
        }
    }

    handleClickSubmitForm(event) {
        this.chapterRecord = event.detail.record;

        if (this.skipSubmitConfirmation) {
            this.submitChapter();
        } else {
            this.confirmationMessage = this.chapterInfo.isLeadAdvisor
                ? this.labels.cepLeadAdvisorSubmitConfirmationMsg
                : this.labels.cepStudentSubmitConfirmationMsg;
            this.isOpenConfirmationModal = true;
        }
    }

    async handleChangeModalStatus(event) {
        if (event.detail.isConfirmed) {
            this.submitChapter();
        }

        this.isOpenConfirmationModal = false;
    }

    async submitChapter() {
        if (this.chapterInfo.chapterRepresentPicklist.isDisabled === false) {
            const picklistValue = this.querySelector(
                `lightning-combobox[data-name="${this.chapterInfo.chapterRepresentPicklist.apiName}"]`
            ).value;
            const textValue = this.querySelector(
                `lightning-input[data-name="${this.chapterInfo.chapterRepresentText.apiName}"]`
            ).value;
            const totalStudents = this.querySelector(
                `lightning-input[data-name="${this.chapterInfo.numberOfUnduplicatedStudent.apiName}"]`
            ).value;

            this.chapterRecord[this.chapterInfo.chapterRepresentPicklist.apiName] = picklistValue;
            this.chapterRecord[this.chapterInfo.chapterRepresentText.apiName] = textValue;
            this.chapterRecord[this.chapterInfo.numberOfUnduplicatedStudent.apiName] = totalStudents;
        }

        await this.saveChapterRecord();
        this.isLoading = false;
        window.location.reload();
    }

    handleChangeStatus() {
        this.skipSubmitConfirmation = true;

        const form = this.querySelector('c-form');
        form.hideSubmitButton = true;
        form.submit();
    }

    async saveChapterRecord() {
        this.isLoading = true;
        try {
            await updateChapter({ chapterExcellenceProgram: this.chapterRecord });
            showSuccessToast(this, this.labels.cepChapterExcellenceSuccessUpdateMessage);
        } catch (error) {
            showErrors(this, error);
        }
    }

    overrideLookupFields() {
        if (!this.chapterInfo.isSchoolUser) {
            this.querySelector('c-form').setOverrides([
                { name: 'ChapterSecretary__c', value: null, placeholder: 'Unknown' },
                { name: 'ChapterPresident__c', value: null, placeholder: 'Unknown' },
                { name: 'ChapterAdvisor__c', value: null, placeholder: 'Unknown' },
                { name: 'CampusAdministrator__c', value: null, placeholder: 'Unknown' }
            ]);
        }
    }

    setUpFormOverrides() {
        if (!this.isVisible) {
            return;
        }

        this.overrides = overrideFormFields(this);
    }

    async generateFormAssessments() {
        this.assessmentFormInfo = generateFormAssessments();
    }

    async generateImageAssessments() {
        this.assessmentImageInfo = generateImageAssessments(this);
    }

    displayAssessmentsFieldNames() {
        const sections = this.querySelectorAll('.assessment-field-names-container');

        sections.forEach((section) => {
            const dataAnchor = SKILL_INDEX_TO_SECTION[section.dataset.skillIndex];
            const formNameSection = this.querySelector(`.section[data-anchor="${dataAnchor}"]`);

            formNameSection.insertBefore(section, formNameSection.children[1]);
        });
    }

    displaySkillsTotalPointsSections() {
        const sections = this.querySelectorAll('.assessment-skill-total-points');

        sections.forEach((section) => {
            const dataAnchor = SKILL_INDEX_TO_SECTION[section.dataset.skillIndex];
            const formNameSection = this.querySelector(`.section[data-anchor="${dataAnchor}"]`);
            formNameSection.appendChild(section);
        });
    }

    displayFormAssessments() {
        const comboboxes = this.querySelectorAll('c-cep-judge-assessment-container[data-category="form"]');

        for (let i = 0; i < comboboxes.length; i++) {
            const cb = comboboxes[i];
            const formElement = this.querySelector(
                `.slds-form__item[role="listitem"] > c-form-field[data-name="${cb.dataset.name}"]`
            )?.parentElement;

            if (formElement) {
                formElement.style.display = 'flex';
                formElement.style.gap = '25px';
                formElement.style.justifyContent = 'space-between';
                formElement.appendChild(cb);
            } else {
                cb.remove();
            }
        }
    }

    displayImageAssessments() {
        const comboboxes = this.querySelectorAll(
            '.images-assessments-stash c-cep-judge-assessment-container[data-category="images"]'
        );

        for (let i = 0; i < comboboxes.length; i++) {
            const cb = comboboxes[i];
            const imageContainer = this.querySelector(
                `.app-upload-image-container[data-skill-name="${cb.dataset.name}"] .app-image-container`
            );
            const imageDescription = this.querySelector(
                `.app-upload-image-container[data-skill-name="${cb.dataset.name}"] .app-image-read-only-description`
            );

            if (!imageContainer) {
                cb.remove();
                continue;
            }

            const targetContainer = cb.dataset.fieldCategory === 'Image' ? imageContainer : imageDescription;
            targetContainer.style.display = 'flex';
            targetContainer.style.gap = '25px';
            targetContainer.style.justifyContent = 'space-between';

            if (cb.dataset.fieldCategory === 'Image') {
                targetContainer.style.alignItems = 'end';
            }

            targetContainer.appendChild(cb);
        }
    }

    recalculateTotalPoints(event) {
        let skillTotalPoints = 0;
        const skillIndex = event.detail;
        const totalPointsElements = this.querySelectorAll(
            `c-cep-judge-assessment-container[data-skill-index="${skillIndex}"] .assessment-total-point`
        );

        totalPointsElements.forEach((element) => {
            skillTotalPoints += Number(element.textContent);
        });

        const skillTotalPointsSection = this.querySelector(
            `.assessment-skill-total-points[data-skill-index="${skillIndex}"] span`
        );

        skillTotalPointsSection.textContent = `${this.labels.cepAssessmentTotalPointsColumn} ${skillTotalPoints}`;
    }

    async saveAssessments() {
        const assessments = this.querySelectorAll('c-cep-judge-assessment-container');
        const isValid = validateAssessments(assessments);

        if (isValid) {
            this.showAssessmentSaveButton = false;

            let groupedAssessments = [
                { Skill__c: this.labels.cepAssessmentPersonalSkills },
                { Skill__c: this.labels.cepAssessmentWorkplaceSkills },
                { Skill__c: this.labels.cepAssessmentTechnicalSkills }
            ];

            assessments.forEach((assessmentItem) => {
                Object.assign(groupedAssessments[assessmentItem.dataset.skillIndex], assessmentItem.getAssessments());
            });

            await saveAssessments({ assessmentResults: groupedAssessments, chapterId: this.recordId });
            showSuccessToast(this, this.labels.cepAssessmentSuccessToastMessage);
            navigateToHomePage(this);
        }
    }
}

import { LightningElement, api } from 'lwc';

import cepAssessmentYourPointsColumn from '@salesforce/label/c.cepAssessmentYourPointsColumn';
import cepAssessmentTotalPointsPossibleColumn from '@salesforce/label/c.cepAssessmentTotalPointsPossibleColumn';
import cepAssessmentWeightAreaColumn from '@salesforce/label/c.cepAssessmentWeightAreaColumn';

export default class CepAssessmentStash extends LightningElement {
    @api assessmentFormInfo;
    @api assessmentImageInfo;

    sections;

    labels = {
        cepAssessmentYourPointsColumn,
        cepAssessmentTotalPointsPossibleColumn,
        cepAssessmentWeightAreaColumn
    };

    connectedCallback() {
        this.generateSections();
    }

    generateSections() {
        const sections = [];

        for (let i = 0; i < 3; i++) {
            sections.push({ skillIndex: i });
        }

        this.sections = sections;
    }

    handleChangeTotalPoints(event) {
        this.passTotalPointsEvent(event.target.dataset.skillIndex);
    }

    passTotalPointsEvent(skillIndex) {
        this.dispatchEvent(new CustomEvent('changetotalpoints', { detail: skillIndex }));
    }
}

<template>
    <div class="assessment-stash-container">
        <div class="form-assessment-stash">
            <template for:each={assessmentFormInfo} for:item="assessment" for:index="index">
                <c-cep-judge-assessment-container
                    data-name={assessment.inputName}
                    data-category="form"
                    data-skill-index={assessment.skillIndex}
                    comboboxes={assessment.comboboxes}
                    key={assessment.index}
                    onchangetotalpoints={handleChangeTotalPoints}
                ></c-cep-judge-assessment-container>
            </template>
        </div>

        <div class="images-assessments-stash">
            <template for:each={assessmentImageInfo} for:item="imgAssessments" for:index="index">
                <c-cep-judge-assessment-container
                    data-name={imgAssessments.inputName}
                    data-category="images"
                    data-field-category={imgAssessments.fieldCategory}
                    data-skill-index={imgAssessments.skillIndex}
                    comboboxes={imgAssessments.comboboxes}
                    onchangetotalpoints={handleChangeTotalPoints}
                    key={imgAssessments.index}
                ></c-cep-judge-assessment-container>
            </template>
        </div>

        <div class="sections-stash">
            <template for:each={sections} for:item="section" for:index="index">
                <div class="assessment-skill-total-points" data-skill-index={section.skillIndex} key={section.index}>
                    <span class="assessment-skill-total-points__text"></span>
                </div>

                <div class="assessment-field-names-container" data-skill-index={section.skillIndex} key={section.index}>
                    <div class="assessment-field-names">
                        <p class="assessment-field-name__text">{labels.cepAssessmentWeightAreaColumn}</p>
                        <p class="assessment-field-name__text">{labels.cepAssessmentYourPointsColumn}</p>
                        <p class="assessment-field-name__text">{labels.cepAssessmentTotalPointsPossibleColumn}</p>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

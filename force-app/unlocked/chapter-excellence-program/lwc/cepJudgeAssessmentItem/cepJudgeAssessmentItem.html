<template>
    <div class="assessment-title">{title}</div>

    <template for:each={tableData} for:item="table" for:index="index">
        <div class={table.class} key={table.index}>
            <table class="assessment-table">
                <thead>
                    <tr>
                        <th if:true={table.isShowHeader} class="assessment-main-table-header" colspan="8">
                            {table.mainHeader}
                        </th>
                    </tr>
                    <tr>
                        <th class="assessment-column"></th>
                        <th class="assessment-column">{labels.cepAssessmentWeightAreaColumn}</th>
                        <template for:each={table.pointsColumns} for:item="column" for:index="index">
                            <th class="assessment-column" key={column.index}>{column}</th>
                        </template>
                        <th class="assessment-column">{labels.cepAssessmentTotalPointsPossibleColumn}</th>
                    </tr>
                </thead>
                <tbody>
                    <template for:each={table.rows} for:item="row" for:index="index">
                        <tr class="assessment-rows" key={row.index}>
                            <template for:each={row.rowsInfo} for:item="rowsInfo" for:index="index">
                                <td rowspan={rowsInfo.rowspan} class={rowsInfo.class} key={rowsInfo.index}>
                                    <lightning-formatted-rich-text
                                        value={rowsInfo.value}
                                    ></lightning-formatted-rich-text>
                                </td>
                            </template>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </template>
</template>

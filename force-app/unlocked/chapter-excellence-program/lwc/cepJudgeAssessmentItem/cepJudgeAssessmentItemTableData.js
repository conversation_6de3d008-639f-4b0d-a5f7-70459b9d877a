export function generateTables() {
    let tables = [];
    tables.push(generateFrameworkOrEssentialTable());
    tables.push(generatePlanOfActionTable());
    tables.push(generateActivityOutcomesTable());
    tables.push(generateActivityGoalsEvaluationTable());
    tables.push(...generateDocumentationAndQualityTables());
    return tables;
}

function generateFrameworkOrEssentialTable() {
    const pointsColumns = ['High Points 3 points', 'High Points 2 points', 'High Points 1 points'];

    const rows = [
        {
            inputName: 'FrameworkEssentialFWEE__c',
            rowsInfo: [
                { value: 'FW/EE Goal (Provide ONE intentional FW/EE Smart Goal).' },
                { value: 2, class: 'assessment-row-center' },
                {
                    value: 'Framework and Essential Element are clearly included in the SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Framework and Essential Element are clearly included in the SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Framework and Essential Element are clearly included in the SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                { value: 6, class: 'assessment-row-center' }
            ]
        },
        {
            inputName: 'FrameworkEssentialGoalNumberOne__c',
            rowsInfo: [
                { value: 'Goal #1 (Activity)' },
                { value: 1, class: 'assessment-row-center' },
                {
                    value: 'Goal is well written and utilizes all 5 components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Goal is vague and does not utilize all the components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Goal is poorly written and does not utilize the components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                { value: 3, class: 'assessment-row-center' }
            ]
        },
        {
            inputName: 'FrameworkEssentialGoalNumberTwo__c',
            rowsInfo: [
                { value: 'Goal #2 (Activity)' },
                { value: 1, class: 'assessment-row-center' },
                {
                    value: 'Goal is well written and utilizes all 5 components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Goal is vague and does not utilize all the components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Goal is poorly written and does not utilize the components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                { value: 3, class: 'assessment-row-center' }
            ]
        },
        {
            inputName: 'FrameworkEssentialGoalNumberThree__c',
            rowsInfo: [
                { value: 'Goal #3 (Activity)' },
                { value: 1, class: 'assessment-row-center' },
                {
                    value: 'Goal is well written and utilizes all 5 components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Goal is vague and does not utilize all the components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                {
                    value: 'Goal is poorly written and does not utilize the components of a SMART goal. (Specific, Measurable, Attainable, Realistic and Timely)'
                },
                { value: 3, class: 'assessment-row-center' }
            ]
        }
    ];

    return {
        mainHeader: 'Framework/Essential Element and Activity Goals',
        pointsColumns: pointsColumns,
        rows: rows,
        class: 'assesment-table-container',
        isShowHeader: true
    };
}

function generatePlanOfActionTable() {
    const table = {
        mainHeader: 'Plan of Action',
        pointsColumns: ['High Points 10-8 points', 'Mid Points 7-4 points', 'Low Points 3-0 points'],
        rows: [
            {
                inputName: 'PlanOfAction__c',
                rowsInfo: [
                    { value: 'Plan of action' },
                    { value: 3, class: 'assessment-row-center' },
                    {
                        value: '<p><span style="font-weight: 400;">Plan of action clearly identifies:</span></p><ul><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">How the activity and FW/EE were chosen.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">What EE content was taught and how was it taught.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">Who will perform the duties to meet the four goals (committee and individual assignments).</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">What needs to be accomplished to meet the four goals.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">The timeline for completing assignments which meet the goals. Where the activity took place.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">The activity budget.</span></li></ul>'
                    },
                    {
                        value: '<p><span style="font-weight: 400;">Plan of action only addresses part of:</span></p><ul><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">How the activity and FW/EE were chosen.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">How you taught the FW/EE.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">Who will perform the duties to meet the four goals (committee and individual assignments).</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">What needs to be accomplished to meet the four goals.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">The timeline for completing assignments which meet the goals. Where the activity took place.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">The activity budget.</span></li></ul>'
                    },
                    {
                        value: '<p><span style="font-weight: 400;">Plan of action does not identify:</span></p><ul><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">How the activity and FW/EE were chosen.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">How you taught the FW/EE.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">Who will perform the duties to meet the four goals (committee and individual assignments).</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">What needs to be accomplished to meet the four goals.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">The timeline for completing assignments which meet the goals. Where the activity took place.</span></li><li style="font-weight: 400;" aria-level="1"><span style="font-weight: 400;">The activity budget.</span></li></ul>'
                    },
                    { value: 30, class: 'assessment-row-center' }
                ]
            }
        ],
        class: 'assesment-table-container',
        isShowHeader: true
    };

    return table;
}

function generateActivityOutcomesTable() {
    const table = {
        mainHeader: 'FW/EE and Activity Outcomes',
        pointsColumns: ['High Points 15-11 points', 'Mid Points 10-6 points', 'Low Points 5-0 points'],
        rows: [
            {
                inputName: 'WhatWasAccomplished__c',
                rowsInfo: [
                    { value: 'What was accomplished? (Impact to community, school, members, etc.)' },
                    { value: 1, class: 'assessment-row-center' },
                    {
                        value: 'Response clearly states the purpose of the activity and describes what was accomplished.'
                    },
                    {
                        value: 'Response vaguely states the purpose of the activity and vaguely describes what was accomplished.'
                    },
                    {
                        value: 'Response does not or poorly states the purpose of the activity and poorly describes what was accomplished.'
                    },
                    { value: 15, class: 'assessment-row-center' }
                ]
            },
            {
                inputName: 'RelatedToFrameworkAndEssential__c',
                rowsInfo: [
                    {
                        value: 'Related to Framework component and Essential Elements. (Impact on student current and future growth; next steps for further growth)'
                    },
                    { value: 1, class: 'assessment-row-center' },
                    {
                        value: 'Response clearly states and describes how students demonstrated or mastered the Essential Element of the Framework component.'
                    },
                    {
                        value: 'Response vaguely states and describes how students demonstrated or mastered the Essential Element of the Framework component.'
                    },
                    {
                        value: 'Response poorly states how students demonstrated or mastered the Essential Element of the Framework component.'
                    },
                    { value: 15, class: 'assessment-row-center' }
                ]
            }
        ],
        class: 'assesment-table-container',
        isShowHeader: true
    };

    return table;
}

function generateActivityGoalsEvaluationTable() {
    const table = {
        mainHeader: 'FW/EE and Activity Goals/Evaluation/Results',
        pointsColumns: ['High Points 3 points', 'Mid Points 2 points', 'Low Points 1-0 points'],
        rows: [
            {
                inputName: 'FWEEGoal__c',
                rowsInfo: [
                    { value: 'FW/EE Goal' },
                    { value: 2, class: 'assessment-row-center' },
                    {
                        value: 'Response clearly states whether all goals were exceeded, met or unmet. If all aspects of SMART goals were met, clearly stated by how much and if exceeded. If goals were unmet, circumstances or reasoning as to why is explained in detail and what would be done differently next time to meet the goals.',
                        rowspan: 4
                    },
                    {
                        value: 'Response vaguely states whether all goals were exceeded, met or unmet. If only portions of aspects of SMART goals were met, it states by how much. If goals were unmet, circumstances or reasoning as to why is explained and what would be done differently next time is identified',
                        rowspan: 4
                    },
                    {
                        value: 'Response does not or poorly states whether all aspects of SMART goals were exceeded, met or unmet. It does not identify how much the goal was met by or does not include the circumstances as to why the goal was unmet.',
                        rowspan: 4
                    },
                    { value: 6, class: 'assessment-row-center' }
                ]
            },
            {
                inputName: 'GoalNumberOne__c',
                rowsInfo: [
                    { value: 'Goal #1 (Activity)' },
                    { value: 1, class: 'assessment-row-center' },
                    { value: 3, class: 'assessment-row-center' }
                ]
            },
            {
                inputName: 'GoalNumberTwo__c',
                rowsInfo: [
                    { value: 'Goal #2 (Activity)' },
                    { value: 1, class: 'assessment-row-center' },
                    { value: 3, class: 'assessment-row-center' }
                ]
            },
            {
                inputName: 'GoalNumberThree__c',
                rowsInfo: [
                    { value: 'Goal #3 (Activity)' },
                    { value: 1, class: 'assessment-row-center' },
                    { value: 3, class: 'assessment-row-center' }
                ]
            }
        ],
        class: 'assesment-table-container',
        isShowHeader: true
    };

    return table;
}

function generateDocumentationAndQualityTables() {
    const firstTable = {
        mainHeader: 'Application Documentation and Quality',
        pointsColumns: ['High Points 3 points', 'Mid Points 2 points', 'Low Points 1-0 points'],
        rows: [
            {
                inputName: 'PhotoOrCaption__c',
                rowsInfo: [
                    { value: 'Photo/caption' },
                    { value: 2, class: 'assessment-row-center' },
                    {
                        value: 'A quality photo illustrating the chapter’s activity with a caption that clearly describes the activity and how the Essential Element was demonstrated.'
                    },
                    {
                        value: 'A photo partially illustrating the chapter’s activity with a caption that vaguely describes the activity and how the Essential Element was demonstrated.'
                    },
                    {
                        value: 'A poor-quality photo with a caption that does not describe the activity and how the Essential Element was demonstrated. The submission was a collage and not a single photo.'
                    },
                    { value: 6, class: 'assessment-row-center' }
                ]
            }
        ],
        class: 'assesment-tables-container',
        isShowHeader: true
    };

    const secondTable = {
        pointsColumns: ['High Points 4 points', 'Mid Points 3-2 points', 'Low Points 1-0 points'],
        rows: [
            {
                inputName: 'SpellingGrammar__c',
                rowsInfo: [
                    { value: 'Spelling Grammar' },
                    { value: 1, class: 'assessment-row-center' },
                    {
                        value: 'No errors or slight errors in grammar or spelling that distract the reader from the content.'
                    },
                    { value: 'Major errors in spelling and grammar that distract the reader from the content.' },
                    { value: 'Excessive errors in spelling and grammar that distract the reader from the content.' },
                    { value: 4, class: 'assessment-row-center' }
                ]
            }
        ],
        class: 'assesment-table-container',
        isShowHeader: false
    };

    return [firstTable, secondTable];
}

.app-upload-image-title-name {
    font-size: 20px;
}

.app-upload-image-container {
    display: grid;
    gap: 10px;
    padding-bottom: 20px;
    justify-items: left;
    font-size: 14px;
}

.app-image-container {
    width: 100%;
    height: 100%;
}

.app-image-wrapper {
    width: 25%;
    border-radius: 13px;
    border: 1px solid #e8e8e8;
    position: relative;
}

.app-image-wrapper::before {
    content: "";
    display: block;
    padding-bottom: 100%;
}

.app-image-logo {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.app-image-read-only-description {
    color: #000000;
}

.app-image-read-only-description, .app-image-description {
    width: 100%;
}

.app-image-read-only-description lightning-textarea {
    width: 75%;
}

@media (max-width: 700px) {
    .app-image-container, .app-image-wrapper {
        width: 100%;
    }
  }
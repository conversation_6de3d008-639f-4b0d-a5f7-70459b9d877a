<template>
    <div class="app-upload-image-container" data-skill-name={skillName}>
        <div class="app-upload-image-title-name">{skillName}</div>

        <div if:true={isPossibleUploadImage} class="app-image-description">
            <lightning-textarea 
                label={labels.cepActivityPhotoEditTextArea}
                value={photoDescription}
                onchange={changeDescription}
                required>
            </lightning-textarea>
        </div>

        <lightning-file-upload
            if:true={isPossibleUploadImage}
            label={labels.cepActivityPhotoAttachFileButton}
            name="fileUploader"
            data-skill-name={skillName}
            accept={acceptedFormats}
            record-id={recordId}
            onuploadfinished={handleUploadFinished}
            multiple="false"
            disabled={isEmptyDescription}
        ></lightning-file-upload>

        <lightning-button
            if:true={isPossibleDeleteImage}
            label={labels.cepActivityPhotoDeleteButtonLabel}
            title={labels.cepActivityPhotoDeleteButtonLabel}
            icon-name="utility:delete"
            variant="destructive"
            onclick={deleteLogoImageId}
        ></lightning-button>

        <div if:true={imageId} class="app-image-container">
            <div class="app-image-wrapper">
                <img class="app-image-logo" src={imageUrl} onclick={showImageModal} />
            </div>
        </div>

        <div if:true={imageId} class="app-image-read-only-description">
            <lightning-textarea
                label={labels.cepActivityPhotoReadOnlyTextArea}
                max-length="550"
                value={photoDescription}
                disabled>
            </lightning-textarea>
        </div>

        <div if:true={isEmptyImage} class="app-image-empty-image">
            <p class="app-image-empty-image-text">{labels.cepActivityPhotoEmpty}</p>
        </div>
    </div>
</template>
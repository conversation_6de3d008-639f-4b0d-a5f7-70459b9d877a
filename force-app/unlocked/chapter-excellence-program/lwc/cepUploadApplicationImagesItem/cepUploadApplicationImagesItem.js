import { LightningElement, api } from 'lwc';
import labels from './labels';
import APPLICATION_IMAGE_MODAL from './cepApplicationImageModal';

export default class CepUploadApplicationImageItem extends LightningElement {
    @api hasEditPermissions;
    @api imageInfo;
    @api skillName;
    @api recordId;
    imageId;
    photoDescription;

    labels = labels;

    isLoading = false;
    acceptedFormats = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.bmp'];

    connectedCallback() {
        if (this.imageInfo) {
            this.imageId = this.imageInfo.LatestPublishedVersionId;
            this.photoDescription = this.imageInfo.Description;
        }
    }

    changeDescription(event) {
        this.photoDescription = event.target.value;
    }

    handleUploadFinished(event) {
        this.imageId = event.detail.files[0].contentVersionId;
        const customEvent = new CustomEvent(
            "finishload",
            { detail: {
                documentId : event.detail.files[0].documentId,
                skillTitle : this.skillName,
                description : this.photoDescription
            }}
        );
        this.dispatchEvent(customEvent);
    }

    deleteLogoImageId() {
        const customEvent = new CustomEvent(
            "deleteimage",
            { detail: {
                contentVersionId : this.imageId
            }}
        );
        this.dispatchEvent(customEvent);
        this.imageId = null;
    }

    getImageVersionURL() {
        let baseUrl = window.location.protocol + '//' + window.location.hostname;
        return baseUrl + '/sfc/servlet.shepherd/version/download/';
    }

    async showImageModal() {
        await APPLICATION_IMAGE_MODAL.open({ imageUrl: this.imageUrl, size: 'full' });
    }

    get imageUrl() {
        return this.getImageVersionURL() + this.imageId;
    }

    get isPossibleUploadImage() {
        return this.hasEditPermissions && !this.imageId;
    }

    get isPossibleDeleteImage() {
        return this.hasEditPermissions && this.imageId;
    }

    get isEmptyImage() {
        return !this.hasEditPermissions && !this.imageId;
    }

    get isEmptyDescription() {
        return this.photoDescription == null || this.photoDescription == '';
    }
}
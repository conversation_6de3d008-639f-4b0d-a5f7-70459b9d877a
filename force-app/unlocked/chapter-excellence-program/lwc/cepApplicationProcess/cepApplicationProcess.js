import { LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { showErrors } from 'c/errorHandler';

import checkVisibility from '@salesforce/apex/cep_ApplicationProcessCtrl.checkVisibility';
import createApplicationProcess from '@salesforce/apex/cep_ApplicationProcessCtrl.createApplicationProcess';

import cepChapterExcellenceButtonLabel from '@salesforce/label/c.cepChapterExcellenceButtonLabel';
import cepChapterExcellenceSuccessMessage from '@salesforce/label/c.cepChapterExcellenceSuccessMessage'; 

const CHAPTER_OBJECT_NAME = 'ChapterExcellenceProgram__c';

export default class extends NavigationMixin(LightningElement) {
    isVisible;
    applicationProcessInfo;
    isLoading = true;

    labels = {
        cepChapterExcellenceButtonLabel,
        cepChapterExcellenceSuccessMessage
    }

    async connectedCallback() {
        await this.checkButtonVisibility();
        this.isLoading = false;
    }

    async checkButtonVisibility() {
        this.isLoading = true;

        try {
            const data = await checkVisibility();
            this.isVisible = data;
        } catch(error) {
            showErrors(this, error);
        }
    }

    async onClickButton() {
        await this.getApplicationProcessInfo();
        this.isLoading = false;

        if (this.applicationProcessInfo && this.applicationProcessInfo.chapterId) {
            this.navigateToChapterId();

            if (this.applicationProcessInfo.isNewChapter) {
                this.showSuccessToast();
            }
        }
    }

    async getApplicationProcessInfo() {
        this.isLoading = true;

        try {
            const data = await createApplicationProcess();
            this.applicationProcessInfo = data;
        } catch(error) {
            showErrors(this, error);
        }
    }

    showSuccessToast() {
        const event = new ShowToastEvent({
            title: 'Success!',
            message: this.labels.cepChapterExcellenceSuccessMessage,
            variant: 'success'
        });
        this.dispatchEvent(event);
    }

    navigateToChapterId() {
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: this.applicationProcessInfo.chapterId,
                objectApiName: CHAPTER_OBJECT_NAME,
                actionName: 'view'
            }
        });
    }
}

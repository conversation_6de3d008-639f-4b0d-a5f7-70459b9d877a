<template>
    <div class="slds-form-element slds-m-bottom_small slds-size_1-of-1 slds-form_horizontal">
        <div class="slds-form-element__control slds-grid slds-wrap">
            <div class="slds-size_1-of-2 slds-m-right_xx-small">
                <lightning-combobox
                    data-name={chapterInfo.chapterRepresentPicklist.apiName}
                    label={chapterInfo.chapterRepresentPicklist.label}
                    value={chapterInfo.chapterRepresentPicklist.value}
                    placeholder=""
                    options={representWorkOptions}
                    disabled={chapterInfo.chapterRepresentPicklist.isDisabled}
                ></lightning-combobox>
            </div>
    
            <div class="slds-size_1-of-2">
                <lightning-input
                    data-name={chapterInfo.chapterRepresentText.apiName}
                    value={chapterInfo.chapterRepresentText.value}
                    type="text"
                    disabled={chapterInfo.chapterRepresentText.isDisabled}
                ></lightning-input>
            </div>
        </div>
    </div>
    
    <template for:each={chapterInfo.readOnlyFields} for:item="field" for:index="index">
        <div key={field.index} class="slds-form-element slds-m-bottom_small slds-size_1-of-2">
            <lightning-input
                label={field.label}
                value={field.value}
                type="text"
                disabled
            ></lightning-input>
        </div>
    </template>

    <div class="slds-form-element slds-m-bottom_medium slds-size_1-of-2">
        <lightning-input
            data-name={chapterInfo.numberOfUnduplicatedStudent.apiName}
            label={chapterInfo.numberOfUnduplicatedStudent.label}
            value={chapterInfo.numberOfUnduplicatedStudent.value}
            type="number"
            disabled={chapterInfo.numberOfUnduplicatedStudent.isDisabled}
        ></lightning-input>
    </div>
</template>
import { LightningElement, track, api } from 'lwc';
import getUserAssessment from '@salesforce/apex/as_AssessmentProcessCtrl.getUserAssessment';
import saveUserAnswer from '@salesforce/apex/as_AssessmentProcessCtrl.saveUserAnswer';
import finishAssessment from '@salesforce/apex/as_AssessmentProcessCtrl.finishAssessment';
import labels from './labels';
export default class extends LightningElement {
    @api recordId;
    @track currentQuestionIndex = 0;
    intervalId;
    labels = labels;
    questions;
    remainingSeconds = 0;
    endTime;

    get formattedTime() {
        return new Date(this.remainingSeconds * 1000).toISOString().substring(11, 19);
    }

    get disableConfirmButton() {
        return this.questions.some((item) => !item.selectedAnswerId);
    }

    get disableNxtButton() {
        return this.currentQuestionIndex === this.questions.length - 1;
    }

    get currentQuestion() {
        return this.questions[this.currentQuestionIndex] || {};
    }

    connectedCallback() {
        this.loadAssessmentDetail();
    }

    loadAssessmentDetail() {
        getUserAssessment({ recordId: this.recordId })
            .then((res) => {
                this.questions = res.questions;
                this.endTime = Date.now() / 1000 + res.remainingTime;
                this.formatQuestions();
                this.startTimer();
            })
            .catch((error) => {
                console.error(error);
            });
    }

    formatQuestions() {
        this.questions = this.questions.map((element) => {
            if (this.currentQuestion.questionId === element.questionId) {
                return { ...element, variant: 'brand-outline' };
            } else if (element.selectedAnswerId) {
                return { ...element, variant: 'brand' };
            }
            return { ...element, variant: '' };
        });
    }

    startTimer() {
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        this.intervalId = setInterval(() => {
            this.remainingSeconds = Math.round(this.endTime - Date.now() / 1000);
            if (this.remainingSeconds <= 0) {
                clearInterval(this.intervalId);
                this.handleTimeExpired();
            }
        }, 1000);
    }

    handleTimeExpired() {
        this.finishAssessment();
        // eslint-disable-next-line no-alert
        window.alert(this.labels.asAssessmentTimeIsUpText);
    }

    handleSubmit() {
        // eslint-disable-next-line no-alert
        const isConfirmed = window.confirm(this.labels.asAssessmentCompleteExamMessage);
        if (isConfirmed) {
            clearInterval(this.intervalId);
            this.finishAssessment();
        }
    }

    finishAssessment() {
        finishAssessment({ resultId: this.recordId }).then(() => {
            this.dispatchEvent(new CustomEvent('finishassessment'));
        });
    }

    handleOptionChange(event) {
        this.questions[this.currentQuestionIndex].selectedAnswerId = event.target.value;
        this.saveAnswer();
    }

    saveAnswer() {
        saveUserAnswer({
            resultId: this.recordId,
            answerId: this.currentQuestion.selectedAnswerId,
            questionId: this.currentQuestion.questionId
        }).catch((error) => {
            console.error(error);
        });
    }

    handlePrevious() {
        if (this.currentQuestionIndex > 0) {
            this.currentQuestionIndex--;
            this.formatQuestions();
        }
    }

    handleNext() {
        if (this.currentQuestionIndex < this.questions.length - 1) {
            this.currentQuestionIndex++;
            this.formatQuestions();
        }
    }

    handleQuestionClick(event) {
        const questionIndex = event.target.dataset.index;
        if (!isNaN(questionIndex) && questionIndex >= 0 && questionIndex < this.questions.length) {
            this.currentQuestionIndex = +questionIndex;
            this.formatQuestions();
        }
    }
}

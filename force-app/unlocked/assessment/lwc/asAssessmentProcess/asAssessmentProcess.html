<template>
    <div if:true={questions} class="slds-p-around_small">
        <div class="as-header slds-p-bottom_small">
            <div class="slds-text-heading_medium">{labels.asAssessmentTimeRemainingLabel} {formattedTime}</div>
            <div class="slds-text-align_right">
                <p>{labels.asAssessmentSubmitText}</p>
                <lightning-button
                    variant="brand"
                    label={labels.asAssessmentSubmitButton}
                    onclick={handleSubmit}
                    disabled={disableConfirmButton}
                ></lightning-button>
            </div>
        </div>
        <div class="question-list">
            <template for:each={questions} for:item="question" for:index="index">
                <lightning-button
                    key={question.questionIndex}
                    variant={question.variant}
                    label={question.questionIndex}
                    data-index={index}
                    onclick={handleQuestionClick}
                ></lightning-button>
            </template>
        </div>
        <div class="slds-p-top_small">
            <div class="slds-form-element">
                <label class="slds-form-element__label slds-text-title_bold" for="answers">{currentQuestion.title}</label>
                <div class="slds-m-vertical_x-small" if:true={currentQuestion.body}>
                    <lightning-formatted-rich-text value={currentQuestion.body}></lightning-formatted-rich-text>
                </div>
                <div class="slds-m-vertical_x-small">{currentQuestion.technicalStandards}</div>
                <div class="slds-form-element__control">
                    <lightning-radio-group
                        id="answers"
                        variant="label-hidden"
                        name="radioGroup"
                        options={currentQuestion.answers}
                        value={currentQuestion.selectedAnswerId}
                        type="radio"
                        onchange={handleOptionChange}
                    ></lightning-radio-group>
                </div>
            </div>
        </div>
        <div class="navigation slds-p-top_small">
            <lightning-button label={labels.asAssessmentPreviousButton} onclick={handlePrevious}></lightning-button>
            <lightning-button
                label={labels.asAssessmentNextButton}
                variant="brand"
                onclick={handleNext}
                disabled={disableNxtButton}
            ></lightning-button>
        </div>
    </div>
</template>

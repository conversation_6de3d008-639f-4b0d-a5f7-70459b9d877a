import asAssessmentTimeRemainingLabel from '@salesforce/label/c.as_AssessmentTimeRemainingLabel';
import asAssessmentSubmitText from '@salesforce/label/c.as_AssessmentSubmitText';
import asAssessmentSubmitButton from '@salesforce/label/c.as_AssessmentSubmitButton';
import asAssessmentPreviousButton from '@salesforce/label/c.as_AssessmentPreviousButton';
import asAssessmentNextButton from '@salesforce/label/c.as_AssessmentNextButton';
import asAssessmentTimeIsUpText from '@salesforce/label/c.as_AssessmentTimeIsUpText';
import asAssessmentCompleteExamMessage from '@salesforce/label/c.as_AssessmentCompleteExamMessage';

export default {
    asAssessmentTimeRemainingLabel,
    asAssessmentSubmitText,
    asAssessmentSubmitButton,
    asAssessmentPreviousButton,
    asAssessmentNextButton,
    asAssessmentTimeIsUpText,
    asAssessmentCompleteExamMessage
};

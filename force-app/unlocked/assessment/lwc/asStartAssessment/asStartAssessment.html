<template>
    <div class="container slds-m-around_medium">
        <lightning-spinner if:true={isLoading} alternative-text="Loading" size="small"></lightning-spinner>
        <div class="slds-m-bottom_medium">{assessmentDescription}</div>
        <div>
            {labels.asAssessmentAgreeMessage}
            <lightning-input
                type="checkbox"
                label={labels.asAssessmentAgreeButton}
                value={agree}
                onchange={handleAgreeChange}
                class="slds-p-bottom_medium"
            ></lightning-input>
        </div>

        <div class="passcode-instruction">{labels.asAssessmentEnterPassCodeMessage}</div>

        <lightning-input
            type="text"
            placeholder={labels.asAssessmentEnterPassCodeLabel}
            variant="label-hidden"
            value={passCode}
            onchange={handlePassCodeChange}
        ></lightning-input>

        <lightning-button
            class="start-button slds-p-top_medium"
            label={labels.asAssessmentStartButton}
            variant="brand"
            onclick={handleStartClick}
            disabled={disableStartButton}
        ></lightning-button>
    </div>
</template>

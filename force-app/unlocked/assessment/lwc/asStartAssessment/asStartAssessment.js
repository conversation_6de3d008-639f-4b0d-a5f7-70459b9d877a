import { LightningElement, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import startAssessment from '@salesforce/apex/as_StartAssessmentCtrl.startAssessment';
import getAssessmentDescription from '@salesforce/apex/as_StartAssessmentCtrl.getAssessmentDescription';
import labels from './labels';

export default class extends LightningElement {
    @api recordId;
    labels = labels;
    agree = false;
    isLoading = false;
    passCode;
    assessmentDescription;

    get disableStartButton() {
        return !this.agree || !this.passCode;
    }

    connectedCallback() {
        this.loadData();
    }

    loadData() {
        this.isLoading = true;
        getAssessmentDescription({ recordId: this.recordId })
            .then((res) => {
                this.assessmentDescription = res;
            })
            .catch((error) => {
                this.handleError(error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    handleAgreeChange(event) {
        this.agree = event.target.checked;
    }

    handlePassCodeChange(event) {
        this.passCode = event.target.value;
    }

    handleStartClick() {
        this.isLoading = true;
        startAssessment({
            recordId: this.recordId,
            passCode: this.passCode
        })
            .then(() => {
                this.finishStart();
            })
            .catch((error) => {
                this.handleError(error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    handleError(error) {
        const evt = new ShowToastEvent({
            title: 'Error',
            message: error.body.message,
            variant: 'error'
        });
        this.dispatchEvent(evt);
    }

    finishStart() {
        this.dispatchEvent(new CustomEvent('finishstart'));
    }
}

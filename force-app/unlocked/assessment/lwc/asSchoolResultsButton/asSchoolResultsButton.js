import { api, LightningElement, wire } from "lwc";
import getResults from '@salesforce/apex/as_SchoolResultCtrl.getResults';
import { showErrors } from 'c/errorHandler';

export default class AsSchoolResultsButton extends LightningElement {
    @api conferenceId;
    @api stretch;
    @api variant;
    @api label;
    @api pageApiName;
    @api queryParamsJson;
    show = false;

    @wire(getResults, { recordId: '$conferenceId' })
    wiredResults({ data, error }) {
        if (data?.isResultsAvailable === true) {
            console.log('data', data);
            this.show = true;
        } else if (error) {
            console.error(error);
            showErrors(this, error);
            this.show = false;
        }
    }
}
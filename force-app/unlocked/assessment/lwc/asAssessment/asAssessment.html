<template>
    <div class="slds-is-relative">
        <lightning-spinner lwc:if={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>

        <template lwc:elseif={isAvailable}>
            <c-as-start-assessment
                lwc:if={showStart}
                record-id={recordId}
                onfinishstart={loadData}
            ></c-as-start-assessment>

            <c-as-assessment-process
                lwc:elseif={showProcess}
                record-id={recordId}
                onfinishassessment={loadData}
            ></c-as-assessment-process>

            <c-as-assessment-result lwc:elseif={showResult}></c-as-assessment-result>
        </template>

        <template lwc:else>
            <span>{labels.assessmentNotAvailableMessage}</span>
        </template>
    </div>
</template>

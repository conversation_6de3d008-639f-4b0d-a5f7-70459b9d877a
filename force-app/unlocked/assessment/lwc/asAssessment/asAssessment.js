import { LightningElement, api } from 'lwc';
import getComponentVisibility from '@salesforce/apex/as_AssessmentCtrl.getComponentVisibility';

import assessmentNotAvailableMessage from '@salesforce/label/c.as_AssessmentNotAvailableMessage';

export default class extends LightningElement {
    @api recordId;
    showStart = false;
    showProcess = false;
    showResult = false;
    isAvailable = false;
    isLoading = true;

    labels = {
        assessmentNotAvailableMessage
    }

    connectedCallback() {
        this.loadData();
    }

    loadData() {
        this.isLoading = true;
        getComponentVisibility({ recordId: this.recordId })
            .then((res) => {
                this.showStart = res.showStart;
                this.showProcess = res.showProcess;
                this.showResult = res.showResult;
                this.isAvailable = res.isAvailable;
            })
            .catch(console.error)
            .finally(() => {
                this.isLoading = false;
            })
    }
}

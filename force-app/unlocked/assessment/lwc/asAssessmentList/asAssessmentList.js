import { LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { showErrors } from 'c/errorHandler';

import getUserAssessments from '@salesforce/apex/as_AssessmentListCtrl.getUserAssessments';

import noAssessmentsMsg from '@salesforce/label/c.as_NoAssessmentsMessage';
import nameColumnLabel from '@salesforce/label/c.as_AssessmentListNameColumnLabel';
import openDatesColumnLabel from '@salesforce/label/c.as_AssessmentListOpenDatesColumnLabel';
import resultColumnLabel from '@salesforce/label/c.as_AssessmentListResultColumnLabel';

export default class extends NavigationMixin(LightningElement) {
    assessments = [];
    useDistricts = false;
    showScores = false;
    isLoading = false;

    labels = {
        noAssessmentsMsg,
        nameColumnLabel,
        openDatesColumnLabel,
        resultColumnLabel
    };

    connectedCallback() {
        this.isLoading = true;
        getUserAssessments()
            .then(({ assessments, showScores, useDistricts }) => {
                this.useDistricts = useDistricts;
                this.showScores = showScores;
                if (this.useDistricts) {
                    assessments.forEach((assessment) => {
                        assessment.openDates = assessment.formattedOpenDate + ' - ' + assessment.formattedCloseDate;
                    });
                }
                this.assessments = assessments;
            })
            .catch(error => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    handleLinkClick(event) {
        event.preventDefault();
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: event.target.dataset.id,
                actionName: 'view'
            }
        });
    }

    get hasAssessments() {
        return !!this.assessments.length;
    }
}

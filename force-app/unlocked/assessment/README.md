## About

The Assessment package provides the ability to create specific assessments and send them to participants so they are able to
pass exams. Exams are passcode-protected and have a configurable time limit to complete them. Questions might have unlimited
answers but only a single answer must be correct. Each correct answer has a specific score, so in the assessment result,
it is possible to see the total gained points depends on answer complexity.

## Installation

Install latest version of package via link in the [changelog file](./CHANGELOG.md).

Or via command line:

```shell
sf package install -o MyOrgAlias -p 04t.. -k {password}
```

## Test data

Run [this script](../../../scripts/apex/assessmentTestData.apex) as anonymous apex. It will create the assessment record, and
questions/answers for it.

## Permission sets

| Permission Set Name    | Description                                                                                                               |
| ---------------------- | :------------------------------------------------------------------------------------------------------------------------ |
| AS Admin Permissions   | Designed for admin users, providing full access to assessment functionality. Create, edit, delete and manage assessments. |
| AS Can Pass Assessment | Provides the ability to pass assessment.                                                                                  |

## Usage

-   Assign permission sets to users according to the needed permissions.
-   In experience builder add the Assessment component to the `as_Result__c` object's detail page.
-   Create the `Assessment__c` record and add necessary questions (`as_Question__c`) and answers (`as_Answer__c`).
-   To link an assessment with specific user, create `as_Result__c` record, and fill `Participant__c` field.
-   When logged in as participant, go to the `as_Result__c` object's detail page and start exam.

## Manual Steps

To use districts for assessments, you should create a sharing rule for the District Open Dates object
Those records should be shared from Internal users to Partner users.
Example:
![openDateSharingRule.png](../../../img/assessment/openDateSharingRule.png)

To added Reset Result and Enable Random Questions go setup -> Object Manager
Assessment -> Page Layouts -> Select layout -> take the "Enable Random Questions" action from "Mobile & Lightning Actions" and put it to the "Salesforce Mobile and Lightning Experience Actions" section
Assessment Result -> Page Layouts -> Select layout -> take the "Reset Result" action from "Mobile & Lightning Actions" and put it to the "Salesforce Mobile and Lightning Experience Actions" section
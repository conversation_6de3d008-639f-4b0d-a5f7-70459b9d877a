public with sharing class as_AssessmentCtrl {
    private static Map<String, Boolean> assessmentComponentsVisibility = new Map<String, Boolean>{
        'showStart' => false,
        'showProcess' => false,
        'showResult' => false,
        'isAvailable' => false
    };

    @AuraEnabled(Cacheable=false)
    public static Map<String, Boolean> getComponentVisibility(Id recordId) {
        try {
            as_Result__c result = as_AssessmentSelector.getResult(recordId);
            assessmentComponentsVisibility.put('showStart', result.StartTime__c == null);
            assessmentComponentsVisibility.put('showProcess', showAssessmentProcess(result));
            assessmentComponentsVisibility.put('showResult', showAssessmentResult(result));
            assessmentComponentsVisibility.put('isAvailable', as_AssessmentService.isAssessmentAvailable(recordId));
            return assessmentComponentsVisibility;
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }

    private static Boolean showAssessmentProcess(as_Result__c result) {
        Datetime start = result.StartTime__c;
        Integer toCompleteTime = result.Assessment__r.TimeToComplete__c.intValue();
        Integer extendedTime = result.ExtendedTime__c != null ? result.ExtendedTime__c.intValue() : 0;

        if (start == null || result.EndTime__c != null) {
            return false;
        }

        if (System.now() > start.addMinutes(toCompleteTime + extendedTime)) {
            return false;
        }

        return true;
    }

    private static Boolean showAssessmentResult(as_Result__c result) {
        Datetime start = result.StartTime__c;
        Integer toCompleteTime = result.Assessment__r.TimeToComplete__c.intValue();
        Integer extendedTime = result.ExtendedTime__c != null ? result.ExtendedTime__c.intValue() : 0;

        if (result.EndTime__c != null) {
            return true;
        }

        if (start != null && System.now() > start.addMinutes(toCompleteTime + extendedTime)) {
            return true;
        }

        return false;
    }
}
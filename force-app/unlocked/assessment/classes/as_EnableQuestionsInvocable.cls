public with sharing class as_EnableQuestionsInvocable {
    @InvocableMethod
    public static void enableRandomQuestions(List<Id> assessmentIds) {
        Decimal questionCountToActivate = as_Settings__c.getInstance().NumberOfRandomActiveQuestions__c ?? 0;
        List<as_Question__c> allQuestions = as_AssessmentSelector.getQuestions(assessmentIds).values();
        Map<Id, List<as_Question__c>> questionsByAssessmentIds = new Map<Id, List<as_Question__c>>();
        for (as_Question__c question : allQuestions) {
            question.IsActive__c = false;
            if (!questionsByAssessmentIds.containsKey(question.Assessment__c)) {
                questionsByAssessmentIds.put(question.Assessment__c, new List<as_Question__c>());
            }
            questionsByAssessmentIds.get(question.Assessment__c).add(question);
        }
        for (Id assessmentId : questionsByAssessmentIds.keySet()) {
            List<as_Question__c> questions = questionsByAssessmentIds.get(assessmentId);
            randomSorting(questions);
            for (Integer i = 0; i < questions.size(); i++) {
                if (i == questionCountToActivate) {
                    break;
                }
                questions.get(i).IsActive__c = true;
            }
        }
        update allQuestions;
    }

    private static void randomSorting(List<SObject> sObjects) {
        Integer size = sObjects.size();
        for(Integer i = 0; i < size; i++) {
            Integer randomIndex = Math.mod(Math.abs(Crypto.getRandomInteger()), size);
            SObject sobj = sObjects.get(i);
            sObjects[i] = sObjects.get(randomIndex);
            sObjects[randomIndex] = sobj;
        }
    }
}
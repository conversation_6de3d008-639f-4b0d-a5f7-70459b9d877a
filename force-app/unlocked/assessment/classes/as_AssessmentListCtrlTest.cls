@IsTest
public with sharing class as_AssessmentListCtrlTest {
    private static String districtValue = '';

    @TestSetup
    static void setup() {
        List<PicklistEntry> picklistEntries = Account.District__c.getDescribe().getPicklistValues();
        as_TestUtils.createAdminUser();
        System.runAs(as_TestUtils.getAdmin()) {
            if (!picklistEntries.isEmpty()) {
                districtValue = picklistEntries.get(0).value;
            }

            Contact memberContact = as_TestUtils.createContact('member acc', districtValue, 'test', 'member');
            as_TestUtils.createMember(memberContact.Id);

            composeData();
        }
    }

    @IsTest
    static void getUserAssessmentsWithoutUseDistricts() {
        insert new as_Settings__c(UseDistricts__c = false);
        System.runAs(as_TestUtils.getMember()) {
            Test.startTest();
            as_AssessmentListCtrl.Response response = as_AssessmentListCtrl.getUserAssessments();
            Test.stopTest();

            Assert.isFalse(response.useDistricts, 'UseDistrict custom setting must be false for this test method');
            Assert.isFalse(response.assessments.isEmpty(), 'Assessment record list must not be empty for a student');
            Assert.areEqual(2, response.assessments.size(), 'list should contains 2 records');
        }
    }

    @IsTest
    static void getUserAssessmentsWithUseDistricts() {
        insert new as_Settings__c(UseDistricts__c = true);
        System.runAs(as_TestUtils.getMember()) {
            Test.startTest();
            as_AssessmentListCtrl.Response response = as_AssessmentListCtrl.getUserAssessments();
            Test.stopTest();

            Assert.isTrue(response.useDistricts, 'UseDistrict custom setting must be true for this test method');
            Assert.isFalse(response.assessments.isEmpty(), 'Assessment record list must not be empty for a student');
            Assert.areEqual(1, response.assessments.size(), 'list should contains one record');
            Assert.areEqual(
                'test assessment with open dates',
                response.assessments.get(0).name,
                'The required record should be found'
            );
        }
    }

    @IsTest
    static void getPassedUserAssessmentsWithResultsAndWithoutUseDistricts() {
        insert new as_Settings__c(
            UseDistricts__c = false,
            ShowPassedAssessments__c = true,
            ShowScoreForAssessments__c = true,
            ShowScoreAfterNDays__c = 0
        );
        System.runAs(as_TestUtils.getMember()) {
            Test.startTest();
            as_AssessmentListCtrl.Response response = as_AssessmentListCtrl.getUserAssessments();
            Test.stopTest();

            Assert.isFalse(response.useDistricts, 'UseDistrict custom setting must be false for this test method');
            Assert.isTrue(
                response.showScores,
                'ShowScoreForAssessments custom setting must be true for this test method'
            );
            Assert.isFalse(response.assessments.isEmpty(), 'Assessment record list must not be empty for a student');
            Assert.areEqual(4, response.assessments.size(), 'list should contains 4 records');
        }
    }

    @IsTest
    static void getPassedUserAssessmentsWithResultsAndWithUseDistricts() {
        insert new as_Settings__c(
            UseDistricts__c = true,
            ShowPassedAssessments__c = true,
            ShowScoreForAssessments__c = true,
            ShowScoreAfterNDays__c = 0
        );
        System.runAs(as_TestUtils.getMember()) {
            Test.startTest();
            as_AssessmentListCtrl.Response response = as_AssessmentListCtrl.getUserAssessments();
            Test.stopTest();

            Assert.isTrue(response.useDistricts, 'UseDistrict custom setting must be true for this test method');
            Assert.isTrue(
                response.showScores,
                'ShowScoreForAssessments custom setting must be true for this test method'
            );
            Assert.isFalse(response.assessments.isEmpty(), 'Assessment record list must not be empty for a student');
            Assert.areEqual(2, response.assessments.size(), 'list should contains 2 records');
            Assert.areEqual(
                'test assessment with open dates',
                response.assessments.get(0).name,
                'The required record should be found'
            );
        }
    }

    private static void composeData() {
        User member = as_TestUtils.getMember();

        as_Assessment__c assessmentWithOpenDates = new as_Assessment__c(
            Name = 'test assessment with open dates',
            TimeToComplete__c = 60,
            PassCode__c = '12345'
        );
        as_Assessment__c assessmentWithoutOpenDates = new as_Assessment__c(
            Name = 'test assessment without open dates',
            TimeToComplete__c = 60,
            PassCode__c = '12345'
        );
        as_Assessment__c passedAssessmentWithoutOpenDates = new as_Assessment__c(
            Name = 'test passed assessment without open dates',
            TimeToComplete__c = 60,
            PassCode__c = '12345'
        );
        as_Assessment__c passedAssessmentWithOpenDates = new as_Assessment__c(
            Name = 'test passed assessment with open dates',
            TimeToComplete__c = 60,
            PassCode__c = '12345'
        );
        insert new List<as_Assessment__c>{
            assessmentWithOpenDates,
            assessmentWithoutOpenDates,
            passedAssessmentWithoutOpenDates,
            passedAssessmentWithOpenDates
        };

        insert new List<as_Result__c>{
            new as_Result__c(Participant__c = member.Id, Assessment__c = assessmentWithOpenDates.Id),
            new as_Result__c(Participant__c = member.Id, Assessment__c = assessmentWithoutOpenDates.Id),
            new as_Result__c(
                Participant__c = member.Id,
                Assessment__c = passedAssessmentWithOpenDates.Id,
                EndTime__c = Datetime.now().addDays(-1)
            ),
            new as_Result__c(
                Participant__c = member.Id,
                Assessment__c = passedAssessmentWithoutOpenDates.Id,
                EndTime__c = Datetime.now().addDays(-1)
            )
        };
        insert new List<as_Question__c>{
            new as_Question__c(Assessment__c = passedAssessmentWithOpenDates.Id, Score__c = 5, IsActive__c = true),
            new as_Question__c(Assessment__c = passedAssessmentWithoutOpenDates.Id, Score__c = 5, IsActive__c = true)
        };

        as_DistrictOpenDates__c openDates = new as_DistrictOpenDates__c(
            StartDate__c = Datetime.now().addDays(-1),
            EndDate__c = Datetime.now().addDays(1),
            District__c = districtValue
        );
        as_DistrictOpenDates__c openDatesForPassedAssessment = new as_DistrictOpenDates__c(
            StartDate__c = Datetime.now().addDays(-1),
            EndDate__c = Datetime.now().addDays(1),
            District__c = districtValue
        );
        insert new List<as_DistrictOpenDates__c>{ openDates, openDatesForPassedAssessment };
        insert new List<as_AssessmentDistrictOpenDates__c>{
            new as_AssessmentDistrictOpenDates__c(
                DistrictOpenDates__c = openDates.Id,
                Assessment__c = assessmentWithOpenDates.Id
            ),
            new as_AssessmentDistrictOpenDates__c(
                DistrictOpenDates__c = openDatesForPassedAssessment.Id,
                Assessment__c = passedAssessmentWithOpenDates.Id
            )
        };
        insert new List<as_DistrictOpenDates__Share>{
            new as_DistrictOpenDates__Share(UserOrGroupId = member.Id, ParentId = openDates.Id, AccessLevel = 'read'),
            new as_DistrictOpenDates__Share(
                UserOrGroupId = member.Id,
                ParentId = openDatesForPassedAssessment.Id,
                AccessLevel = 'read'
            )
        };
    }
}
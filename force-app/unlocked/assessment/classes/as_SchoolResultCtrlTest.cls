@IsTest
private class as_SchoolResultCtrlTest {

    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);

            cm_Conference__c conference = new cm_Conference__c(
                    Name = 'Test Conference',
                    IsCompetitionsAvailable__c = true,
                    IsOpenForRegistration__c = true,
                    AssessmentResultsEnabled__c = true
            );
            insert conference;
        }
    }

    @IsTest
    static void testGetResults() {

//        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            cm_Conference__c theConference = [SELECT Id FROM cm_Conference__c WHERE Name = 'Test Conference' LIMIT 1];
            Test.startTest();
            {
                as_SchoolResultCtrl.getResults(theConference.Id);
            }
            Test.stopTest();
//        }
    }

    @IsTest
    static void testError() {
        Boolean hasError = false;

        Test.startTest();
        {
            try {
                as_SchoolResultCtrl.getResults(null);
            } catch (Exception e) {
                hasError = true;
            }
        }
        Test.stopTest();

        Assert.isTrue(hasError, 'Error must exist');
    }
}
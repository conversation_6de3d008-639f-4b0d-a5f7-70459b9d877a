@IsTest
public class as_TestUtils {
    public static final String ADMIN_PERMISSION_SET_NAME = 'as_AdminPermissions';
    public static final String MEMBER_PERMISSION_SET_NAME = 'as_CanPassAssessment';
    private static final String ADMIN_EMAIL = '<EMAIL>';
    private static final String MEMBER_EMAIL = '<EMAIL>';

    public static User createAdminUser() {
        UserRole role = new UserRole(DeveloperName = 'CEO_TEST', Name = 'CEO_TEST');
        insert role;

        User admin = makeUser('Ad', 'Min', ADMIN_EMAIL, 'System Administrator', null);
        admin.UserRoleId = role.Id;
        insert admin;

        Id permissionSetId = [SELECT Id FROM PermissionSet WHERE Name = :ADMIN_PERMISSION_SET_NAME].Id;
        insert new PermissionSetAssignment(PermissionSetId = permissionSetId, AssigneeId = admin.Id);

        return admin;
    }

    public static User getAdmin() {
        return [SELECT Id FROM User WHERE Email = :ADMIN_EMAIL];
    }

    public static User createMember(Id contactId) {
        User member = makeUser('John', 'Doe', MEMBER_EMAIL, 'Portal User', contactId);
        insert member;

        Id permissionSetId = [SELECT Id FROM PermissionSet WHERE Name = :MEMBER_PERMISSION_SET_NAME].Id;
        insert new PermissionSetAssignment(PermissionSetId = permissionSetId, AssigneeId = member.Id);

        return member;
    }

    public static User getMember() {
        return [SELECT Id FROM User WHERE Email = :MEMBER_EMAIL];
    }

    private static User makeUser(String firstName, String lastName, String username, String profileName, Id contactId) {
        Profile profile = [SELECT Id FROM Profile WHERE Name = :profileName];
        return new User(
            Alias = ('a' + Math.random() * 100).substring(1, 6),
            Email = username,
            Username = username,
            EmailEncodingKey = 'UTF-8',
            FirstName = firstName,
            LastName = lastName,
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = profile.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            ContactId = contactId
        );
    }

    public static Contact createContact(String accountName, String accountDistrict, String firstName, String lastName) {
        Account theAccount = new Account(Name = accountName, District__c = accountDistrict);
        insert theAccount;

        Contact theContact = new Contact(FirstName = firstName, LastName = lastName, AccountId = theAccount.Id);
        insert theContact;
        return theContact;
    }

    public static Contact createContact(String accountName, String firstName, String lastName) {
        return createContact(accountName, '', firstName, lastName);
    }
}
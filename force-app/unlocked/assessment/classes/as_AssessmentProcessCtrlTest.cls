@IsTest
public class as_AssessmentProcessCtrlTest {
    @TestSetup
    static void setup() {
        as_TestUtils.createAdminUser();
        System.runAs(as_TestUtils.getAdmin()) {
            Contact theContact = as_TestUtils.createContact('Acme', '<PERSON>', 'Doe');
            createUsers(theContact.Id);
        }
    }

    @Future
    static void createUsers(Id contactId) {
        User member = as_TestUtils.createMember(contactId);

        as_Assessment__c assessment = new as_Assessment__c(Name = 'Test', PassCode__c = '1111', TimeToComplete__c = 60);
        insert assessment;

        as_Question__c question1 = new as_Question__c(
            Assessment__c = assessment.Id,
            Title__c = 'Where do you live?',
            Body__c = 'Where do you live?',
            Score__c = 10,
            IsActive__c = true
        );
        insert question1;
        insert new as_Answer__c(Question__c = question1.Id, Body__c = 'USA', IsCorrect__c = true);
        insert new as_Answer__c(Question__c = question1.Id, Body__c = 'Canada');

        as_Question__c question2 = new as_Question__c(
            Assessment__c = assessment.Id,
            Title__c = 'What is the capital of France?',
            Body__c = 'What is the capital of France?',
            Score__c = 10,
            IsActive__c = true
        );
        insert question2;
        as_Answer__c answer1Question2 = new as_Answer__c(
            Question__c = question2.Id,
            Body__c = 'Paris',
            IsCorrect__c = true
        );
        insert answer1Question2;
        insert new as_Answer__c(Question__c = question2.Id, Body__c = 'London');

        as_Result__c result = new as_Result__c(
            Assessment__c = assessment.Id,
            Participant__c = member.Id,
            ExtendedTime__c = 10,
            StartTime__c = System.now()
        );
        insert result;

        insert new as_ResultAnswer__c(
            Answer__c = answer1Question2.Id,
            Question__c = question2.Id,
            Result__c = result.Id,
            Participant__c = member.Id,
            GainedScore__c = 0
        );
    }

    @IsTest
    static void getUserAssessmentSuccess() {
        User member = as_TestUtils.getMember();
        as_UserAssessmentDTO res = null;
        as_Result__c result = null;

        System.runAs(member) {
            result = [SELECT Id, Assessment__c FROM as_Result__c WHERE Participant__c = :member.Id];
            Test.startTest();
            {
                res = as_AssessmentProcessCtrl.getUserAssessment(result.Id);
            }
            Test.stopTest();
        }

        Assert.areEqual(result.Assessment__c, res.assessmentId, 'Wrong related assessment');
        Assert.areEqual(2, res.questions.size(), 'Wrong number of questions');
        Assert.areNotEqual(null, res.remainingTime, 'Wrong remaining time');
    }

    @IsTest
    static void getUserAssessmentError() {
        User member = as_TestUtils.getMember();
        Boolean isError = false;

        System.runAs(member) {
            Test.startTest();
            {
                try {
                    as_AssessmentProcessCtrl.getUserAssessment('000000000000000000');
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(true, isError, 'Error must exist');
    }

    @IsTest
    static void saveUserAnswerSuccess() {
        User member = as_TestUtils.getMember();

        as_Result__c result = new as_Result__c();
        List<as_ResultAnswer__c> resultsAnswersAfter = new List<as_ResultAnswer__c>();

        System.runAs(member) {
            result = [
                SELECT Id, Assessment__c
                FROM as_Result__c
                WHERE Participant__c = :member.Id
            ];
            List<as_Question__c> question = [
                SELECT Id, (SELECT Id FROM Answers__r)
                FROM as_Question__c
            ];

            Test.startTest();
            {
                as_AssessmentProcessCtrl.saveUserAnswer(result.Id, question[0].Answers__r.get(0).Id, question[0].Id);
            }
            Test.stopTest();

            resultsAnswersAfter = [
                SELECT Id
                FROM as_ResultAnswer__c
                WHERE Result__c = :result.Id AND Participant__c = :member.Id
            ];
        }

        Assert.areEqual(2, resultsAnswersAfter.size(), 'Only two assessment result answers must exist');
    }

    @IsTest
    static void updateUserAnswerSuccess() {
        User member = as_TestUtils.getMember();

        Decimal scoreBefore = 0;
        Decimal scoreAfter = 0;
        System.runAs(member) {
            as_Answer__c answer = [SELECT Id FROM as_Answer__c WHERE Body__c = 'London' LIMIT 1];
            as_ResultAnswer__c answerBefore = [
                SELECT Id, Result__c, Question__c, GainedScore__c
                FROM as_ResultAnswer__c
                WHERE Participant__c = :member.Id
            ];
            scoreBefore = answerBefore.GainedScore__c;

            Test.startTest();
            {
                as_AssessmentProcessCtrl.saveUserAnswer(answerBefore.Result__c, answer.Id, answerBefore.Question__c);
            }
            Test.stopTest();

            as_ResultAnswer__c answerAfter = [
                SELECT GainedScore__c
                FROM as_ResultAnswer__c
                WHERE Id = :answerBefore.Id
            ];
            scoreAfter = answerAfter.GainedScore__c;
        }

        Assert.areEqual(0, scoreBefore, 'Gained score must be 0');
    }

    @IsTest
    static void saveUserAnswerError() {
        User member = as_TestUtils.getMember();
        Boolean isError = false;
        Id wrongId = '000000000000000000';

        System.runAs(member) {
            Test.startTest();
            {
                try {
                    as_AssessmentProcessCtrl.saveUserAnswer(wrongId, wrongId, wrongId);
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(true, isError, 'Error must exist');
    }

    @IsTest
    static void finishAssessmentSuccess() {
        User member = as_TestUtils.getMember();
        Datetime endTime = null;

        System.runAs(member) {
            as_Result__c result = [
                SELECT Id, EndTime__c
                FROM as_Result__c
                WHERE Participant__c = :member.Id
                LIMIT 1
            ];

            Test.startTest();
            {
                as_AssessmentProcessCtrl.finishAssessment(result.Id);
            }
            Test.stopTest();

            endTime = [SELECT EndTime__c FROM as_Result__c WHERE Id = :result.Id].EndTime__c;
        }

        Assert.areNotEqual(null, endTime, 'End time must be populated');
    }

    @IsTest
    static void finishAssessmentError() {
        User member = as_TestUtils.getMember();
        Boolean isError = false;
        Id wrongId = '000000000000000000';

        System.runAs(member) {
            Test.startTest();
            {
                try {
                    as_AssessmentProcessCtrl.finishAssessment(wrongId);
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(true, isError, 'Error must exist');
    }
}
public inherited sharing class as_UserAssessmentDTO {
    @AuraEnabled
    public Id assessmentId;

    @AuraEnabled
    public List<Question> questions;

    @AuraEnabled
    public Long remainingTime;

    public as_UserAssessmentDTO(Id assessmentId, List<Question> questions, Long remainingTime) {
        this.assessmentId = assessmentId;
        this.questions = questions;
        this.remainingTime = remainingTime;
    }

    public class Question {
        @AuraEnabled
        public Integer questionIndex;

        @AuraEnabled
        public Id questionId;

        @AuraEnabled
        public String title;

        @AuraEnabled
        public String body;

        @AuraEnabled
        public String technicalStandards;

        @AuraEnabled
        public List<Answer> answers;

        @AuraEnabled
        public Id selectedAnswerId;

        public Question(Integer questionIndex, as_Question__c question, Id answerId) {
            this.questionIndex = questionIndex;
            this.questionId = question.Id;
            this.title = question.Title__c;
            this.body = question.Body__c;
            this.technicalStandards = question.TechnicalStandards__c;
            this.selectedAnswerId = answerId;
            this.answers = new List<Answer>();
            for (as_Answer__c item : question.Answers__r) {
                this.answers.add(new Answer(item.Id, item.Body__c));
            }
        }
    }

    public class Answer {
        @AuraEnabled
        public Id value;

        @AuraEnabled
        public String label;

        public Answer(Id value, String label) {
            this.value = value;
            this.label = label;
        }
    }
}
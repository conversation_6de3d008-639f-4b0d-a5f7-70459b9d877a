public with sharing class as_SchoolResultCtrl {

    @AuraEnabled(Cacheable=true)
    public static as_SchoolResultCtrl.Response getResults(Id recordId) {
        try {
            cm_Conference__c conference = cm_Selector.getConference(recordId);

            Response theResponse = new Response();
            theResponse.isResultsAvailable = conference.AssessmentResultsEnabled__c;
            theResponse.isAssessmentScoreEnabled = conference.AssessmentScoreEnabled__c;
            theResponse.results = new List<as_Result__c>();
            if (conference.AssessmentResultsEnabled__c) {
                Id schoolId = PortalUser.getAccountId();
                theResponse.results = as_AssessmentSelector.getSchoolResults(recordId, schoolId);
            }

            return theResponse;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    public class Response {
        @AuraEnabled
        public Boolean isResultsAvailable;
        @AuraEnabled
        public Boolean isAssessmentScoreEnabled;
        @AuraEnabled
        public List<as_Result__c> results;
    }

}
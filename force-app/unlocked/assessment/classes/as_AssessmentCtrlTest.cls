@IsTest
public class as_AssessmentCtrlTest {
    @TestSetup
    static void setup() {
        as_TestUtils.createAdminUser();
        System.runAs(as_TestUtils.getAdmin()) {
            Contact theContact = as_TestUtils.createContact('Acme', '<PERSON>', 'Doe');
            createUsers(theContact.Id);
        }
    }

    @Future
    static void createUsers(Id contactId) {
        User member = as_TestUtils.createMember(contactId);
        as_Assessment__c assessment = new as_Assessment__c(Name = 'Test', PassCode__c = '1111', TimeToComplete__c = 60);
        insert assessment;
        insert new as_Result__c(Assessment__c = assessment.Id, Participant__c = member.Id);
    }

    @IsTest
    static void showStartAssessmentTrue() {
        User member = as_TestUtils.getMember();

        Map<String, Boolean> res = new Map<String, Boolean>();

        System.runAs(member) {
            as_Result__c result = [SELECT Id FROM as_Result__c WHERE Participant__c = :member.Id];
            Test.startTest();
            {
                res = as_AssessmentCtrl.getComponentVisibility(result.Id);
            }
            Test.stopTest();
        }

        Assert.areEqual(true, res.get('showStart'), 'Wrong response value');
        Assert.areEqual(false, res.get('showProcess'), 'Wrong response value');
        Assert.areEqual(false, res.get('showResult'), 'Wrong response value');
    }

    @IsTest
    static void showProcessAssessmentTrue() {
        User member = as_TestUtils.getMember();

        Map<String, Boolean> res = new Map<String, Boolean>();

        System.runAs(member) {
            as_Result__c result = [SELECT Id FROM as_Result__c WHERE Participant__c = :member.Id];
            result.StartTime__c = System.now();
            update result;

            Test.startTest();
            {
                res = as_AssessmentCtrl.getComponentVisibility(result.Id);
            }
            Test.stopTest();
        }

        Assert.areEqual(false, res.get('showStart'), 'Wrong response value');
        Assert.areEqual(true, res.get('showProcess'), 'Wrong response value');
        Assert.areEqual(false, res.get('showResult'), 'Wrong response value');
    }

    @IsTest
    static void getComponentVisibilityError() {
        Boolean isError = false;
        Id wrongId = '000000000000000000';

        Test.startTest();
        {
            try {
                as_AssessmentCtrl.getComponentVisibility(wrongId);
            } catch (Exception ex) {
                isError = true;
            }
        }
        Test.stopTest();

        Assert.areEqual(true, isError, 'Error must exist');
    }
}
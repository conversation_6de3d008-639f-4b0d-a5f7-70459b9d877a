public with sharing class as_StartAssessmentCtrl {
    @AuraEnabled
    public static void startAssessment(Id recordId, String passCode) {
        try {
            as_AssessmentService.startAssessment(recordId, passCode);
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }

    @AuraEnabled
    public static String getAssessmentDescription(Id recordId) {
        try {
            return as_AssessmentSelector.getResult(recordId).Assessment__r.Description__c;
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }
}
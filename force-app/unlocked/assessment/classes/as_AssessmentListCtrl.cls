public with sharing class as_AssessmentListCtrl {
    @AuraEnabled
    public static Response getUserAssessments() {
        try {
            as_Settings__c settings = as_Settings__c.getInstance();
            return new Response(settings.UseDistricts__c, settings.ShowScoreForAssessments__c, getAssessments());
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    private static List<AssessmentDTO> getAssessments() {
        as_Settings__c settings = as_Settings__c.getInstance();
        Boolean isUseDistrictsEnabled = settings.UseDistricts__c;
        List<as_Result__c> results = settings.ShowPassedAssessments__c
            ? as_AssessmentSelector.getResults(UserInfo.getUserId())
            : as_AssessmentSelector.getOpenResults(UserInfo.getUserId());

        if (isUseDistrictsEnabled) {
            String district = [SELECT Account.District__c FROM User WHERE Id = :UserInfo.getUserId()]
            .Account.District__c;
            Map<Id, as_Result__c> assessmentToResult = new Map<Id, as_Result__c>();
            for (as_Result__c result : results) {
                assessmentToResult.put(result.Assessment__c, result);
            }
            List<as_Assessment__c> assessments = as_AssessmentSelector.getAssessmentsWithDistrictsByIdsAndDistrict(
                assessmentToResult.keySet(),
                district
            );

            List<AssessmentDTO> assessmentDTOS = new List<AssessmentDTO>();
            for (as_Assessment__c assessment : assessments) {
                if (!assessment.DistrictOpenDates__r.isEmpty()) {
                    as_Result__c result = assessmentToResult.get(assessment.Id);
                    assessmentDTOS.add(new AssessmentDTO(result, assessment, getGainedScore(result)));
                }
            }
            return assessmentDTOS;
        } else {
            List<AssessmentDTO> assessmentDTOS = new List<AssessmentDTO>();
            for (as_Result__c result : results) {
                assessmentDTOS.add(new AssessmentDTO(result, getGainedScore(result)));
            }
            return assessmentDTOS;
        }
    }

    private static String getGainedScore(as_Result__c result) {
        String gainedScore = '-';
        if (as_Settings__c.getInstance().ShowScoreForAssessments__c && result.EndTime__c != null) {
            Integer daysToAdd = Integer.valueOf(as_Settings__c.getInstance().ShowScoreAfterNDays__c) ?? 0;
            Datetime availableOnDate = result.EndTime__c.addDays(daysToAdd);

            gainedScore = result.EndTime__c >= availableOnDate
                ? result.AnswerScore__c + '/' + result.Assessment__r.MaxAvailableScore__c
                : String.format(Label.as_AssessmentAvailableOnDateMessage, new List<String> {availableOnDate.format()});
        }
        return gainedScore;
    }

    public class AssessmentDTO {
        @AuraEnabled
        public String name;
        @AuraEnabled
        public String assessmentResultId;
        @AuraEnabled
        public String formattedOpenDate;
        @AuraEnabled
        public String formattedCloseDate;
        @AuraEnabled
        public Boolean isActual;
        @AuraEnabled
        public String gainedScore = '-';

        public AssessmentDTO(as_Result__c result, as_Assessment__c assessment, String gainedScore) {
            this.name = result.Assessment__r.Name;
            this.assessmentResultId = result.Id;
            this.gainedScore = gainedScore;
            if (
                assessment != null && !assessment.DistrictOpenDates__r.isEmpty()
            ) {
                as_DistrictOpenDates__c openDate = assessment.DistrictOpenDates__r.get(0).DistrictOpenDates__r;
                this.formattedOpenDate = openDate.StartDate__c.format();
                this.formattedCloseDate = openDate.EndDate__c.format();
                this.isActual = openDate.StartDate__c <= Datetime.now()
                    && openDate.EndDate__c >= Datetime.now()
                    && result.EndTime__c == null;
            } else {
                this.isActual = result.EndTime__c == null;
            }
        }

        public AssessmentDTO(as_Result__c result, String gainedScore) {
            this(result, null, gainedScore);
        }
    }

    public class Response {
        @AuraEnabled
        public Boolean useDistricts;
        @AuraEnabled
        public Boolean showScores;
        @AuraEnabled
        public List<AssessmentDTO> assessments;

        public Response(Boolean useDistricts, Boolean isShowScores, List<AssessmentDTO> assessments) {
            this.useDistricts = useDistricts;
            this.showScores = isShowScores;
            this.assessments = assessments;
        }
    }
}
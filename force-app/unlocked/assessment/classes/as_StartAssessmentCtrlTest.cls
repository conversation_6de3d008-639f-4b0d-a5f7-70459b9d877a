@IsTest
public class as_StartAssessmentCtrlTest {
    @TestSetup
    static void setup() {
        User admin = as_TestUtils.createAdminUser();

        System.runAs(admin) {
            as_Assessment__c assessment = new as_Assessment__c(
                Name = 'Test',
                PassCode__c = '1111',
                TimeToComplete__c = 60,
                Description__c = 'New Assessment'
            );
            insert assessment;
            insert new as_Result__c(Assessment__c = assessment.Id, Participant__c = admin.Id);
        }
    }

    @IsTest
    static void startAssessmentSuccess() {
        User admin = as_TestUtils.getAdmin();

        Datetime startTimeBeforeStartAssessment = null;
        Datetime startTimeAfterStartAssessment = null;

        System.runAs(admin) {
            as_Result__c result = [
                SELECT Id, StartTime__c
                FROM as_Result__c
                WHERE StartTime__c = NULL
                LIMIT 1
            ];
            startTimeBeforeStartAssessment = result.StartTime__c;

            Test.startTest();
            {
                as_StartAssessmentCtrl.startAssessment(result.Id, '1111');
            }
            Test.stopTest();

            result = [SELECT Id, StartTime__c FROM as_Result__c WHERE Id = :result.Id];
            startTimeAfterStartAssessment = result.StartTime__c;
        }

        Assert.areEqual(null, startTimeBeforeStartAssessment, 'Start Time before test must null');
        Assert.areNotEqual(null, startTimeAfterStartAssessment, 'Start Time must be populated');
    }

    @IsTest
    static void startAssessmentError() {
        User admin = as_TestUtils.getAdmin();

        String errorMessage = '';

        System.runAs(admin) {
            as_Result__c result = [
                SELECT Id, StartTime__c
                FROM as_Result__c
                WHERE StartTime__c = NULL
                LIMIT 1
            ];

            Test.startTest();
            {
                try {
                    as_StartAssessmentCtrl.startAssessment(result.Id, '2222'); // Wrong Pass Code
                } catch (Exception e) {
                    errorMessage = e.getMessage();
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(System.Label.as_IncorrectPassCodeMessage, errorMessage, 'Wrong error message');
    }

    @IsTest
    static void getAssessmentDescriptionSuccess() {
        User admin = as_TestUtils.getAdmin();
        String description = '';

        System.runAs(admin) {
            as_Result__c result = [SELECT Id FROM as_Result__c WHERE StartTime__c = NULL LIMIT 1];

            Test.startTest();
            {
                description = as_StartAssessmentCtrl.getAssessmentDescription(result.Id);
            }
            Test.stopTest();
        }

        Assert.areEqual('New Assessment', description, 'Wrong assessment description');
    }

    @IsTest
    static void getAssessmentDescriptionError() {
        User admin = as_TestUtils.getAdmin();
        String errorMessage = '';

        System.runAs(admin) {
            Test.startTest();
            {
                try {
                    as_StartAssessmentCtrl.getAssessmentDescription('000000000000000000');
                } catch (Exception ex) {
                    errorMessage = ex.getMessage();
                }
            }
            Test.stopTest();
        }

        Assert.areNotEqual(null, errorMessage, 'Error message must exist');
    }
}
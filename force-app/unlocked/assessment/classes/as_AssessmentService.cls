public with sharing class as_AssessmentService {
    public static void startAssessment(Id recordId, String passCode) {
        as_Result__c result = as_AssessmentSelector.getResult(recordId);
        if (result.Assessment__r.PassCode__c != passCode && result.StartTime__c == null) {
            throw Error.exception(System.Label.as_IncorrectPassCodeMessage);
        }

        update as user new as_Result__c(Id = recordId, StartTime__c = System.now());
    }

    public static void saveUserAnswer(Id resultId, Id answerId, Id questionId) {
        as_Result__c result = as_AssessmentSelector.getResult(resultId);

        if (result.EndTime__c != null) {
            throw Error.exception(System.Label.as_AssessmentTimeIsUpText);
        }

        List<as_ResultAnswer__c> results = as_AssessmentSelector.getResultAnswer(resultId, questionId);

        as_Question__c question = as_AssessmentSelector.getQuestion(questionId);
        as_Answer__c answer = as_AssessmentSelector.getAnswer(answerId);
        Decimal gainedScore = answer.IsCorrect__c ? question.Score__c : 0;

        if (results.isEmpty()) {
            insert as user new as_ResultAnswer__c(
                Answer__c = answerId,
                Question__c = questionId,
                Result__c = resultId,
                Participant__c = UserInfo.getUserId(),
                GainedScore__c = gainedScore
            );
        } else {
            as_ResultAnswer__c resultAnswer = results.get(0);
            resultAnswer.Answer__c = answerId;
            resultAnswer.GainedScore__c = gainedScore;
            update as user resultAnswer;
        }
    }

    public static as_UserAssessmentDTO getUserAssessment(Id recordId) {
        as_Result__c result = as_AssessmentSelector.getResult(recordId);
        List<as_UserAssessmentDTO.Question> questions = getUserQuestionsWithAnswers(result);
        Long remainingTime = getRemainingTimeInSeconds(result);
        as_UserAssessmentDTO studentAssessment = new as_UserAssessmentDTO(
            result.Assessment__c,
            questions,
            remainingTime
        );

        return studentAssessment;
    }

    public static Boolean isAssessmentAvailable(Id resultId) {
        as_Result__c result = as_AssessmentSelector.getResult(resultId);
        if (result.Participant__c != UserInfo.getUserId()) {
            return false;
        }

        if (as_Settings__c.getInstance().UseDistricts__c) {
            User currentUser = [SELECT Account.District__c FROM User WHERE Id = :UserInfo.getUserId()];
            List<as_DistrictOpenDates__c> openDates = [
                SELECT Id
                FROM as_DistrictOpenDates__c
                WHERE
                    District__c = :currentUser.Account.District__c
                    AND EndDate__c > :Datetime.now()
                    AND StartDate__c < :Datetime.now()
                    AND Id IN (
                        SELECT DistrictOpenDates__c
                        FROM as_AssessmentDistrictOpenDates__c
                        WHERE Assessment__c = :result.Assessment__c
                    )
            ];

            return !openDates.isEmpty();
        }

        return true;
    }

    private static List<as_UserAssessmentDTO.Question> getUserQuestionsWithAnswers(as_Result__c result) {
        List<as_UserAssessmentDTO.Question> listToReturn = new List<as_UserAssessmentDTO.Question>();
        Map<Id, as_Question__c> questions = as_AssessmentSelector.getActiveQuestions(result.Assessment__c);
        Map<Id, as_ResultAnswer__c> userAnswers = getUserAnswers(result.Id);

        Integer questionIndex = 1;
        for (as_Question__c item : questions.values()) {
            Id answerId = userAnswers.get(item.Id)?.Answer__c;
            as_UserAssessmentDTO.Question question = new as_UserAssessmentDTO.Question(questionIndex, item, answerId);
            listToReturn.add(question);
            questionIndex++;
        }

        return listToReturn;
    }

    private static Map<Id, as_ResultAnswer__c> getUserAnswers(Id recordId) {
        Map<Id, as_ResultAnswer__c> answersToReturn = new Map<Id, as_ResultAnswer__c>();
        List<as_ResultAnswer__c> userAnswers = as_AssessmentSelector.getResultAnswers(recordId);

        for (as_ResultAnswer__c item : userAnswers) {
            answersToReturn.put(item.Question__c, item);
        }

        return answersToReturn;
    }

    private static Long getRemainingTimeInSeconds(as_Result__c result) {
        Datetime now = System.now();
        Datetime start = result.StartTime__c;
        Integer toComplete = result.Assessment__r.TimeToComplete__c.intValue();
        Integer extendedTime = result.ExtendedTime__c != null ? result.ExtendedTime__c.intValue() : 0;

        Datetime endTime = start.addMinutes(toComplete + extendedTime);

        return now > endTime ? 0 : (endTime.getTime() - now.getTime()) / 1000;
    }
}
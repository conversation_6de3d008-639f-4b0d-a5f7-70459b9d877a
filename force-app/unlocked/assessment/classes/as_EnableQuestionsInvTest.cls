@IsTest
public with sharing class as_EnableQuestionsInvTest {
    private static final Integer numberOfRandomActiveQuestions = 3;
    @TestSetup
    static void setup() {
        as_TestUtils.createAdminUser();
        System.runAs(as_TestUtils.getAdmin()) {
            as_Assessment__c assessment = new as_Assessment__c(
                Name = 'Test',
                PassCode__c = '1111',
                TimeToComplete__c = 60
            );
            insert assessment;

            List<as_Question__c> questions = new List<as_Question__c>();

            for (Integer i = 0; i < 5; i++) {
                questions.add(
                    new as_Question__c(
                        Title__c = 'test title' + i,
                        Score__c = 5,
                        Assessment__c = assessment.Id,
                        IsActive__c = false
                    )
                );
            }
            insert questions;

            insert new as_Settings__c(NumberOfRandomActiveQuestions__c = numberOfRandomActiveQuestions);
        }
    }

    @IsTest
    static void enableRandomQuestionsTest() {
        Integer enabledQuestionCount = 0;
        System.runAs(as_TestUtils.getAdmin()) {
            as_Assessment__c assessment = [SELECT Id FROM as_Assessment__c LIMIT 1];
            Test.startTest();
            as_EnableQuestionsInvocable.enableRandomQuestions(new List<Id>{ assessment.Id });
            Test.stopTest();
            List<as_Question__c> questions = [
                SELECT Id, IsActive__c
                FROM as_Question__c
                WHERE Assessment__c = :assessment.Id
            ];
            for (as_Question__c question : questions) {
                if (question.IsActive__c) {
                    enabledQuestionCount++;
                }
            }
        }
        System.assertEquals(
            numberOfRandomActiveQuestions,
            enabledQuestionCount,
            'enabled questions count should equals to ' + numberOfRandomActiveQuestions
        );
    }
}
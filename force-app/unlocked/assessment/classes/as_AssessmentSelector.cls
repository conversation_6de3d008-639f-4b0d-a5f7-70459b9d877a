public with sharing class as_AssessmentSelector {
    public static Map<Id, as_Question__c> getActiveQuestions(Set<Id> assessmentIds) {
        return new Map<Id, as_Question__c>(
            [
                SELECT
                    Id,
                    Title__c,
                    Body__c,
                    TechnicalStandards__c,
                    Score__c,
                    Assessment__c,
                    (SELECT Id, Body__c FROM Answers__r)
                FROM as_Question__c
                WHERE Assessment__c IN :assessmentIds AND IsActive__c = TRUE
                WITH USER_MODE
            ]
        );
    }
    public static Map<Id, as_Question__c> getActiveQuestions(Id assessmentId) {
        return getActiveQuestions(new Set<Id>{ assessmentId });
    }

    public static Map<Id, as_Question__c> getQuestions(List<Id> assessmentIds) {
        return new Map<Id, as_Question__c>(
            [
                SELECT Id, Title__c, Body__c, TechnicalStandards__c, Assessment__c, (SELECT Id, Body__c FROM Answers__r)
                FROM as_Question__c
                WHERE Assessment__c IN :assessmentIds
                WITH USER_MODE
            ]
        );
    }

    public static List<as_ResultAnswer__c> getResultAnswers(Id resultId) {
        return [
            SELECT Answer__c, Question__c
            FROM as_ResultAnswer__c
            WHERE Result__c = :resultId AND Participant__c = :UserInfo.getUserId()
            WITH USER_MODE
        ];
    }

    public static as_Result__c getResult(Id recordId) {
        return [
            SELECT
                StartTime__c,
                EndTime__c,
                ExtendedTime__c,
                Assessment__c,
                Assessment__r.PassCode__c,
                Assessment__r.TimeToComplete__c,
                Assessment__r.Description__c,
                Participant__c
            FROM as_Result__c
            WHERE Id = :recordId
            WITH USER_MODE
        ];
    }

    public static List<as_Result__c> getOpenResults(Id participantId) {
        return [
            SELECT
                Id,
                StartTime__c,
                EndTime__c,
                ExtendedTime__c,
                Assessment__c,
                Assessment__r.PassCode__c,
                Assessment__r.TimeToComplete__c,
                Assessment__r.Description__c,
                Assessment__r.Name,
                Assessment__r.MaxAvailableScore__c,
                AnswerScore__c
            FROM as_Result__c
            WHERE Participant__c = :participantId AND EndTime__c = NULL
            WITH USER_MODE
        ];
    }

    public static List<as_Result__c> getSchoolResults(Id conferenceId, Id schoolId) {
        cm_Conference__c conference = cm_Selector.getConference(conferenceId);

        List<cmp_Competition__c> competitions = [
            SELECT AssessmentTech__c, AssessmentSafety__c, AssessmentEmployability__c
            FROM cmp_Competition__c
            WHERE Conference__c = :conferenceId
        ];

        Set<Id> relatedAssessmentIds = new Set<Id>();
        if (conference.EmployabilityAssessment__c != null) {
            relatedAssessmentIds.add(conference.EmployabilityAssessment__c);
        }
        for (cmp_Competition__c competition : competitions) {
            if (competition.AssessmentTech__c != null) {
                relatedAssessmentIds.add(competition.AssessmentTech__c);
            }
            if (competition.AssessmentSafety__c != null) {
                relatedAssessmentIds.add(competition.AssessmentSafety__c);
            }
            if (competition.AssessmentEmployability__c != null) {
                relatedAssessmentIds.add(competition.AssessmentEmployability__c);
            }
        }

        return new WSHelper().getResults(schoolId, relatedAssessmentIds);
    }

    public static List<as_Result__c> getResults(Id participantId) {
        return [
            SELECT
                Id,
                StartTime__c,
                EndTime__c,
                ExtendedTime__c,
                Assessment__c,
                Assessment__r.PassCode__c,
                Assessment__r.TimeToComplete__c,
                Assessment__r.Description__c,
                Assessment__r.Name,
                Assessment__r.MaxAvailableScore__c,
                AnswerScore__c
            FROM as_Result__c
            WHERE Participant__c = :participantId
            WITH USER_MODE
        ];
    }

    public static as_Question__c getQuestion(Id recordId) {
        return [
            SELECT Score__c
            FROM as_Question__c
            WHERE Id = :recordId
            WITH USER_MODE
        ];
    }

    public static as_Answer__c getAnswer(Id recordId) {
        return [
            SELECT IsCorrect__c
            FROM as_Answer__c
            WHERE Id = :recordId
            WITH USER_MODE
        ];
    }

    public static List<as_ResultAnswer__c> getResultAnswer(Id resultId, Id questionId) {
        return [
            SELECT Answer__c, GainedScore__c
            FROM as_ResultAnswer__c
            WHERE Result__c = :resultId AND Question__c = :questionId AND Participant__c = :UserInfo.getUserId()
            WITH USER_MODE
            LIMIT 1
        ];
    }

    public static List<as_Assessment__c> getAssessmentsWithDistrictsByIdsAndDistrict(
        Set<Id> assessmentIds,
        String district
    ) {
        return [
            SELECT
                Id,
                Name,
                (
                    SELECT Id, DistrictOpenDates__r.StartDate__c, DistrictOpenDates__r.EndDate__c
                    FROM DistrictOpenDates__r
                    WHERE
                        DistrictOpenDates__r.EndDate__c > :Datetime.now()
                        AND DistrictOpenDates__r.District__c = :district
                ),
                (
                    SELECT Id, Score__c
                    FROM Questions__r
                    WHERE IsActive__c = TRUE
                )
            FROM as_Assessment__c
            WHERE Id IN :assessmentIds
        ];
    }

    private without sharing class WSHelper {
        public List<as_Result__c> getResults(Id schoolId, Set<Id> assessmentIds) {
            return [
                    SELECT
                            Id,
                            StartTime__c,
                            EndTime__c,
                            Assessment__r.Name,
                            Assessment__r.MaxAvailableScore__c,
                            AnswerScore__c,
                            TotalScore__c,
                            Participant__r.Contact.Name
                    FROM as_Result__c
                    WHERE Participant__r.AccountId = :schoolId AND Assessment__c IN :assessmentIds
            ];
        }
    }
}
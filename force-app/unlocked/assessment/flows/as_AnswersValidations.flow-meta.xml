<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <assignments>
        <name>Count_Answers</name>
        <label>Count Answers</label>
        <locationX>534</locationX>
        <locationY>588</locationY>
        <assignmentItems>
            <assignToReference>TotalNumberOfAnswers</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Retrieve_Related_Answers</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>TotalAnswers</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>CountCorrectAnswers</name>
        <label>CountCorrectAnswers</label>
        <locationX>1189</locationX>
        <locationY>497</locationY>
        <assignmentItems>
            <assignToReference>TotalNumberOfCorrectAnswers</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>RetrieveCorrectAnswers</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CorrectAnswers</targetReference>
        </connector>
    </assignments>
    <customErrors>
        <name>MoreAnswers</name>
        <label>MoreAnswers</label>
        <locationX>1067</locationX>
        <locationY>793</locationY>
        <customErrorMessages>
            <errorMessage>Single answer limit: Each question is allowed to have only one correct answer.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>NoCorrectAnswers</name>
        <label>NoCorrectAnswers</label>
        <locationX>1332</locationX>
        <locationY>791</locationY>
        <customErrorMessages>
            <errorMessage>Add at least one correct answer to proceed.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>NoMoreFiveAnswers</name>
        <label>NoMoreFiveAnswers</label>
        <locationX>785</locationX>
        <locationY>808</locationY>
        <customErrorMessages>
            <errorMessage>Question limit exceeded: A maximum 5 answers are allowed for each question.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Answer</name>
        <label>Answer</label>
        <locationX>767</locationX>
        <locationY>407</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsNewAnswer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Retrieve_Related_Answers</targetReference>
            </connector>
            <label>IsNewAnswer</label>
        </rules>
        <rules>
            <name>IsCorrectChange</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IsCorrect__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RetrieveCorrectAnswers</targetReference>
            </connector>
            <label>IsCorrectChange</label>
        </rules>
    </decisions>
    <decisions>
        <name>CorrectAnswers</name>
        <label>CorrectAnswers</label>
        <locationX>1181</locationX>
        <locationY>673</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>MoreThenOne</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>TotalNumberOfCorrectAnswers</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>MoreAnswers</targetReference>
            </connector>
            <label>MoreThenOne</label>
        </rules>
        <rules>
            <name>LessThenOne</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>TotalNumberOfAnswers</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>5.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>TotalNumberOfCorrectAnswers</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>NoCorrectAnswers</targetReference>
            </connector>
            <label>LessThenOne</label>
        </rules>
    </decisions>
    <decisions>
        <name>TotalAnswers</name>
        <label>TotalAnswers</label>
        <locationX>773</locationX>
        <locationY>590</locationY>
        <defaultConnector>
            <targetReference>RetrieveCorrectAnswers</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>MoreThenFiveAnswers</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>TotalNumberOfAnswers</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>6.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>NoMoreFiveAnswers</targetReference>
            </connector>
            <label>MoreThenFiveAnswers</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>isNew</name>
        <dataType>Boolean</dataType>
        <expression>isNew()</expression>
    </formulas>
    <interviewLabel>Answers Validations {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[AS] Answers Validations</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Retrieve_Related_Answers</name>
        <label>Retrieve Related Answers</label>
        <locationX>534</locationX>
        <locationY>403</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Count_Answers</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Question__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Question__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>as_Answer__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>RetrieveCorrectAnswers</name>
        <label>RetrieveCorrectAnswers</label>
        <locationX>1008</locationX>
        <locationY>497</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CountCorrectAnswers</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Question__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Question__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsCorrect__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>as_Answer__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>650</locationX>
        <locationY>48</locationY>
        <connector>
            <targetReference>Answer</targetReference>
        </connector>
        <object>as_Answer__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>TotalNumberOfAnswers</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>TotalNumberOfCorrectAnswers</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
</Flow>

<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>CheckResultAnswerExists</name>
        <label>Check Result Answer Exists</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>ClearingResultFields</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Exists</defaultConnectorLabel>
        <rules>
            <name>ResultAnswersExists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetResultAnswers</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>DeleteResultAnswers</targetReference>
            </connector>
            <label>Exists</label>
        </rules>
    </decisions>
    <description>Used for the action of resetting the results of the &quot;Result&quot; record</description>
    <environments>Default</environments>
    <interviewLabel>Reset Result {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[AS] Reset Result</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordDeletes>
        <name>DeleteResultAnswers</name>
        <label>Delete Result Answers</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>ClearingResultFields</targetReference>
        </connector>
        <inputReference>GetResultAnswers</inputReference>
    </recordDeletes>
    <recordLookups>
        <name>GetResultAnswers</name>
        <label>Get Result Answers</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckResultAnswerExists</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Result__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>as_ResultAnswer__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>ClearingResultFields</name>
        <label>Clearing Result Fields</label>
        <locationX>182</locationX>
        <locationY>650</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>EndTime__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>ExtendedTime__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>ManualScore__c</field>
        </inputAssignments>
        <inputAssignments>
            <field>StartTime__c</field>
        </inputAssignments>
        <object>as_Result__c</object>
    </recordUpdates>
    <screens>
        <name>ResetConfirmationMessageScreen</name>
        <label>Reset Confirmation Message Screen</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>GetResultAnswers</targetReference>
        </connector>
        <fields>
            <name>ResetConfirmationMessage</name>
            <fieldText>&lt;p&gt;{!$Label.as_AssessmentResultResetConfirmation}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Yes</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>ResetConfirmationMessageScreen</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>

<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <customErrors>
        <name>DuplicateError</name>
        <label>Duplicate Error</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <customErrorMessages>
            <errorMessage>{!$Label.as_AssessmentDistrictOpenDatesDuplicateErrorMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>CheckDuplicate</name>
        <label>Check Duplicate</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsDuplicateExists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetAssessmentDistrictOpenDates</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>DuplicateError</targetReference>
            </connector>
            <label>Is Duplicate Exists</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>[AS] {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[AS] Check Assessment District Open Dates Duplicates</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetAssessmentDistrictOpenDates</name>
        <label>Get Assessment District Open Dates</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckDuplicate</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Assessment__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Assessment__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>DistrictOpenDates__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.DistrictOpenDates__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>as_AssessmentDistrictOpenDates__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetAssessmentDistrictOpenDates</targetReference>
        </connector>
        <object>as_AssessmentDistrictOpenDates__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>

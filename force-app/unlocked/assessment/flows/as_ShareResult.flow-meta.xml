<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>AssessmentAccess</name>
        <label>AssessmentAccess</label>
        <locationX>967</locationX>
        <locationY>786</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>HasNoAssessmentAccess</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>UserHasAssessmentAccess</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CreateAssessmentAccess</targetReference>
            </connector>
            <label>HasNoAssessmentAccess</label>
        </rules>
    </decisions>
    <decisions>
        <name>UserAccess</name>
        <label>User Access</label>
        <locationX>971</locationX>
        <locationY>596</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>HasNoAccess</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>HasAccess</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Sharing_Record</targetReference>
            </connector>
            <label>Has no access</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Share Result {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[AS] Share Result</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>FREE_FORM_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>CreateAssessmentAccess</name>
        <label>CreateAssessmentAccess</label>
        <locationX>977</locationX>
        <locationY>971</locationY>
        <inputAssignments>
            <field>AccessLevel</field>
            <value>
                <stringValue>Read</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>$Record.Assessment__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>UserOrGroupId</field>
            <value>
                <elementReference>$Record.Participant__c</elementReference>
            </value>
        </inputAssignments>
        <object>as_Assessment__Share</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Sharing_Record</name>
        <label>Create Sharing Record</label>
        <locationX>764</locationX>
        <locationY>592</locationY>
        <connector>
            <targetReference>UserAssessmentAccess</targetReference>
        </connector>
        <inputAssignments>
            <field>AccessLevel</field>
            <value>
                <stringValue>Edit</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>UserOrGroupId</field>
            <value>
                <elementReference>$Record.Participant__c</elementReference>
            </value>
        </inputAssignments>
        <object>as_Result__Share</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_User_Access</name>
        <label>Get User Access</label>
        <locationX>978</locationX>
        <locationY>426</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>UserAccess</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>HasEditAccess</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>UserId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Participant__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <object>UserRecordAccess</object>
        <outputAssignments>
            <assignToReference>HasAccess</assignToReference>
            <field>RecordId</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <name>UserAssessmentAccess</name>
        <label>UserAssessmentAccess</label>
        <locationX>764</locationX>
        <locationY>784</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AssessmentAccess</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>HasReadAccess</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>UserId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Participant__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Assessment__c</elementReference>
            </value>
        </filters>
        <object>UserRecordAccess</object>
        <outputAssignments>
            <assignToReference>UserHasAssessmentAccess</assignToReference>
            <field>RecordId</field>
        </outputAssignments>
    </recordLookups>
    <start>
        <locationX>639</locationX>
        <locationY>87</locationY>
        <connector>
            <targetReference>Get_User_Access</targetReference>
        </connector>
        <object>as_Result__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>HasAccess</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>UserHasAssessmentAccess</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>

public with sharing class cm_ConferenceService {

    static public Boolean hasCompetitionPlugin() {
        return Type.forName('cmp_Competition__c') != null;
    }

    static public Integer getLimitOfAdvisorsForSchool(Id conferenceId, Id accountId) {
        cm_Conference__c conference = new cm_ConferenceService.WS().getConference(conferenceId, accountId);
        Decimal theLimit = conference.LimitOfAdvisorsPerSchool__c ?? 0;

        return Integer.valueOf(theLimit - conference.Participants__r.size());
    }

    without sharing class WS {
        public cm_Conference__c getConference(Id conferenceId, Id accountId) {
            return [
                    SELECT Id, LimitOfAdvisorsPerSchool__c, (
                            SELECT Id FROM Participants__r WHERE Contact__r.AccountId = :accountId AND Contact__r.PortalRole__c INCLUDES ('Advisor', 'Lead Advisor')
                    )
                    FROM cm_Conference__c
                    WHERE Id = :conferenceId
            ];
        }
    }

}
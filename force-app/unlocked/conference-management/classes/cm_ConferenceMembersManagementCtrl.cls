public with sharing class cm_ConferenceMembersManagementCtrl {
    @AuraEnabled(Cacheable=false)
    public static List<cm_MembersManagementDTO> getConferenceData(Id conferenceId, Boolean showOnlyApproved) {
        try {
            return cm_MembersManagementService.getConferenceData(conferenceId, showOnlyApproved);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void updateRequests(List<Id> requestIds, Boolean isApproved) {
        try {
            final Boolean USE_LIMITS = Test.isRunningTest() ? true : cmp_Settings__c.getOrgDefaults().EnableLimitsForCompetitions__c;

            if (isApproved && USE_LIMITS) {
                checkLimitsForCompetitions(requestIds); // Throws exception
            }

            cm_MembersManagementService.updateConferenceRequests(requestIds, isApproved);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void sendListOfApprovedMembers(Id conferenceId) {
        try {
            List<cm_MembersManagementDTO> approvedMembers = cm_MembersManagementService.getConferenceData(conferenceId, true);
            cm_MembersManagementService.sendListOfApprovedMembers(
                    conferenceId,
                    approvedMembers
            );
        } catch (Exception e) {
            throw e;
        }
    }

    private static void checkLimitsForCompetitions(List<Id> requestIds) {
        List<cmp_Competition__c> competitions =  new WSHelper().getCompetition(requestIds);
        List<AggregateResult> cmpParticipants = [
                SELECT Competition__c, COUNT(Id) FROM cmp_Participant__c WHERE ConferenceParticipant__c IN :requestIds GROUP BY Competition__c
        ];
        Map<Id, Integer> competitionIdToNewlyAddedParticipants = new Map<Id, Integer>();

        for (AggregateResult AR: cmpParticipants) {
            competitionIdToNewlyAddedParticipants.put((Id) AR.get('Competition__c'), (Integer) AR.get('expr0'));
        }

        String membershipType = cmp_CompetitionsSelectionCtrl.getMembershipTypeForSchool(PortalUser.getAccountId());
        Map<Id, Map<String, Decimal>> currentLimits = cmp_CompetitionsSelectionCtrl.getLimits(competitions, membershipType);

        for (Id key: currentLimits.keySet()) {
            Map<String, Decimal> theLimit = currentLimits.get(key);
            Decimal remainingCompetitors = theLimit.get('remainingCompetitors');
            if (remainingCompetitors - competitionIdToNewlyAddedParticipants.get(key) < 0) {
                throw Error.toLWC('You can not approve participants as the limits for competition are reached.');
            }
        }
    }

    private class WSHelper {
        public List<cmp_Competition__c> getCompetition(List<Id> requestIds) {
            List<cmp_Competition__c> competitions = [
                    SELECT Id, Type__c, LimitOfCompetitors__c, LimitOfTeams__c, IsTeamCompetition__c,
                            LimitOfCompetitorsDirect__c, LimitOfTeamsDirect__c, LimitOfCompetitorsTPP__c, LimitOfTeamsTPP__c,
                    (
                            SELECT Id, ConferenceParticipant__r.RegistrationStatus__c
                            FROM CompetitionParticipants__r
                            WHERE Participant__r.AccountId = :PortalUser.getAccountId()
                    )
                    FROM cmp_Competition__c
                    WHERE Id IN (
                            SELECT Competition__c
                            FROM cmp_Participant__c
                            WHERE ConferenceParticipant__c IN :requestIds
                    )
            ];
            return competitions;
        }

    }

}
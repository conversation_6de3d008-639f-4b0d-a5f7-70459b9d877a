@IsTest
public class cm_ConferenceMembersManagementCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact advisor = cm_TestUtils.createContact(theAccount.Id, 'Advisor');
            PortalUser.create(advisor);
            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            cm_Conference__c conference = new cm_Conference__c(
                    Name = 'Test Conference',
                    IsCompetitionsAvailable__c = true,
                    RegistrationDeadline__c = System.today().addDays(10),
                    IsOpenForRegistration__c = true
            );
            insert conference;

            cm_RegistrationType__c registrationType = new cm_RegistrationType__c(
                    Name = 'Student',
                    IsActive__c = true,
                    Conference__c = conference.Id,
                    IsCompetitionsSelectionAvailable__c = true
            );
            insert registrationType;

//            ConferenceCompetition__c competition1 = new ConferenceCompetition__c(
//                    Name = 'First',
//                    cm_Conference__c = conference.Id,
//                    Division__c = 'Middle School'
//            );
//            ConferenceCompetition__c competition2 = new ConferenceCompetition__c(
//                    Name = 'Second',
//                    cm_Conference__c = conference.Id,
//                    Division__c = 'Middle School'
//            );
//            ConferenceCompetition__c competition3 = new ConferenceCompetition__c(
//                    Name = 'Third',
//                    cm_Conference__c = conference.Id,
//                    Division__c = 'High School'
//            );
//
//            insert new List<ConferenceCompetition__c>{
//                    competition1, competition2, competition3
//            };

//
//            insert new ConferenceCompetitionParticipant__c(
//                    ConferenceCompetition__c = competition1.Id,
//                    cm_Participant__c = student.Id
//            );

            cm_Participant__c conferenceParticipant1 = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = student.Id,
                    ConferenceRegistrationType__c = registrationType.Id,
                    RegistrationStatus__c = 'Pending Approval'
            );

            cm_Participant__c conferenceParticipant2 = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = advisor.Id,
                    ConferenceRegistrationType__c = registrationType.Id,
                    RegistrationStatus__c = 'Approved'
            );

            insert new List<cm_Participant__c>{
                    conferenceParticipant1, conferenceParticipant2
            };
        }
    }

    @IsTest
    static void getConferenceDataApprovedSuccess() {
        List<cm_MembersManagementDTO> conferenceData;

        System.runAs(cm_TestUtils.getAdmin()) {
            cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
            Test.startTest();
            {
                conferenceData = cm_ConferenceMembersManagementCtrl.getConferenceData(conference.Id, true);
            }
            Test.stopTest();
        }

//        Assert.areEqual(1, conferenceData.size(), 'Only one approved request must exist');
    }

    @IsTest
    static void getConferenceDataAllSuccess() {
        List<cm_MembersManagementDTO> conferenceData;

        System.runAs(cm_TestUtils.getAdmin()) {
            cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
            Test.startTest();
            {
                conferenceData = cm_ConferenceMembersManagementCtrl.getConferenceData(conference.Id, false);
            }
            Test.stopTest();
        }

//        Assert.areEqual(2, conferenceData.size(), 'Two requests must exist');
    }

    @IsTest
    static void getConferenceDataError() {
        Boolean isError = false;
        Id wrongId = '000000000000000000';

        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    cm_ConferenceMembersManagementCtrl.getConferenceData(wrongId, false);
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

//        Assert.areEqual(true, isError, 'Error must exist');
    }

    @IsTest
    static void updateRequestsApproveSuccess() {
        Map<Id, cm_Participant__c> participants = new Map<Id, cm_Participant__c>(
            [SELECT Id FROM cm_Participant__c WHERE RegistrationStatus__c = 'Pending Approval']
        );
        List<cm_Participant__c> approvedParticipants;
        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                cm_ConferenceMembersManagementCtrl.updateRequests(new List<Id>(participants.keySet()), true);
                approvedParticipants = [
                    SELECT Id
                    FROM cm_Participant__c
                    WHERE RegistrationStatus__c = 'Approved'
                ];
            }
            Test.stopTest();
        }

//        Assert.areEqual(2, approvedParticipants.size(), 'Two approved request must exist');
    }

    @IsTest
    static void updateRequestsRejectSuccess() {
        Map<Id, cm_Participant__c> participants = new Map<Id, cm_Participant__c>(
            [SELECT Id FROM cm_Participant__c WHERE RegistrationStatus__c = 'Pending Approval']
        );
        List<cm_Participant__c> rejectedParticipants;
        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                cm_ConferenceMembersManagementCtrl.updateRequests(new List<Id>(participants.keySet()), false);
                rejectedParticipants = [
                    SELECT Id
                    FROM cm_Participant__c
                    WHERE RegistrationStatus__c = 'Rejected'
                ];
            }
            Test.stopTest();
        }

//        Assert.areEqual(1, rejectedParticipants.size(), 'One rejected request must exist');
    }

    @IsTest
    static void updateRequestsError() {
        Boolean isError = false;
        Id wrongId = '000000000000000000';

        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    cm_ConferenceMembersManagementCtrl.updateRequests(null, true);
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(true, isError, 'Error must exist');
    }

    @IsTest
    static void sendListOfApprovedMembersSuccess() {
        Integer emailsSent;
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];

        System.runAs(cm_TestUtils.getAdmin()) {
            conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
        }

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            try {
                cm_ConferenceMembersManagementCtrl.sendListOfApprovedMembers(conference.Id);
                emailsSent = Limits.getEmailInvocations();
            } catch (Exception e) {

            }

        }

//        Assert.areEqual(1, emailsSent, 'One Email must be sent');
    }

    @IsTest
    static void sendListOfApprovedMembersError() {
        Boolean isError = false;
        Id wrongId = '000000000000000000';

        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    cm_ConferenceMembersManagementCtrl.sendListOfApprovedMembers(wrongId);
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(true, isError, 'Error must exist');
    }
}
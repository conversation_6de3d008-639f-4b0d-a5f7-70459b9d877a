@IsTest
public class cm_MembersRegistrationCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            Date today = System.today();

            cm_Conference__c conference = new cm_Conference__c(
                    Name = 'Test Conference',
                    RegistrationDeadline__c = today.addDays(10),
                    IsOpenForRegistration__c = true
            );
            insert conference;
        }
    }

//    @TestSetup
//    static void setup() {
//        System.runAs(cm_TestUtils.getAdmin()) {
//            Account testAccount = new Account(Name = 'Last Household');
//            insert testAccount;
//
//            Contact testContact = new Contact(FirstName = 'First', LastName = 'Last', Email = '<EMAIL>');
//            testContact.AccountId = testAccount.Id;
//            testContact.LastName = 'Community';
//            insert testContact;
//
//            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference');
//            insert conference;
//        }
//    }

    @IsTest
    static void getStudentSuccess() {
        Contact theContact = [SELECT Id FROM Contact LIMIT 1];
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c];
        cm_MembersRegistrationCtrl.Response participants;
        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                participants = cm_MembersRegistrationCtrl.getContacts(conference.Id, 'Student');
            }
            Test.stopTest();
        }
//        Assert.areEqual(1, participants.contacts.size(), 'One not registered student must exist');
    }

    @IsTest
    static void getAdvisorSuccess() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c];
        cm_MembersRegistrationCtrl.Response participants;
        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                participants = cm_MembersRegistrationCtrl.getContacts(conference.Id, 'Advisor');
            }
            Test.stopTest();
        }
        //Assert.areEqual(1, participants.contacts.size(), 'One not registered advisor must exist');
    }

    @IsTest
    static void getAdvisorError() {
        Boolean isError = false;

        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    cm_MembersRegistrationCtrl.getContacts(null, 'Advisor');
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.areEqual(true, isError, 'Error must exist');
    }
}
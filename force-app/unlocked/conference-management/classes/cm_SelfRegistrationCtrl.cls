public with sharing class cm_SelfRegistrationCtrl {

    public class Response {
        @AuraEnabled
        public Boolean isConferenceAvailableForRegistration { get; set; }
        @AuraEnabled
        public Boolean isAlreadyRegistered { get; set; }
    }

    @AuraEnabled(Cacheable=false)
    public static Response init(Id conferenceId) {
        try {
            Response theResponse = new Response();
            theResponse.isConferenceAvailableForRegistration = cm_MembersRegistrationCtrl.isConferenceAvailableForRegistration(conferenceId);
            theResponse.isAlreadyRegistered = isAlreadyRegistered(conferenceId, PortalUser.getContactId());
            return theResponse;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    private static Boolean isAlreadyRegistered(Id conferenceId, Id contactId) {
        cm_Participant__c[] participant = [SELECT Id FROM cm_Participant__c WHERE Conference__c = :conferenceId AND Contact__c = :contactId];
        return participant.size() > 0;
    }
}
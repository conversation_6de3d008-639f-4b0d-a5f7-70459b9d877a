@IsTest
public class cm_TestUtils {
    public static final String ADMIN_PERMISSION_SET_NAME = 'CM_AdminPermissions';
    public static final String ADMIN_EMAIL = '<EMAIL>';
    public static final String COMMUNITY_USER_PROFILE_NAME = 'Customer Community Plus User';
    public static final String INTERNAL_USER_EMAIL = '<EMAIL>';
    public static final String LEAD_ADVISOR_PERMISSION_SET_NAME = 'cm_LeadAdvisorPermissions';
    public static final String STUDENT_PERMISSION_SET_NAME = 'cm_StudentPermissions';
    public static final String ADVISOR_PERMISSION_SET_NAME = 'cm_AdvisorPermissions';

    public static void insertSettings() {
        insert new CoreSetting__c(
                PortalUserDefaultProfileName__c = 'Partner Community User',
                AdvisorPermissionSets__c = 'cm_AdvisorPermissions',
                LeadAdvisorPermissionSets__c = 'cm_LeadAdvisorPermissions',
                StudentPermissionSets__c = 'cm_StudentPermissions',
                OrgWideEmailId__c = null
        );

        insert new cm_Settings__c(
                ExhibitorPriceOrganizationIndustry__c = 100
        );
    }

    public static User createAdminUser() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];

        UserRole role = new UserRole(DeveloperName = 'CEO_TEST', Name = 'CEO_TEST');
        insert role;

        User admin = new User(
                ProfileId = p.Id,
                FirstName = 'John',
                LastName = 'Doe',
                Email = ADMIN_EMAIL,
                Username = ADMIN_EMAIL,
                Alias = 'jdoe123',
                TimeZoneSidKey = 'America/Chicago',
                EmailEncodingKey = 'UTF-8',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US',
                UserRoleId = role.Id
        );
        insert admin;

        PermissionSet PS = [SELECT Id FROM PermissionSet WHERE Name = :ADMIN_PERMISSION_SET_NAME];
        insert new PermissionSetAssignment(AssigneeId = admin.Id, PermissionSetId = PS.Id);

        return admin;
    }

    public static User getAdmin() {
        return [SELECT Id FROM User WHERE Email = :ADMIN_EMAIL];
    }

    public static User getUserByRole(String role) {
        return [SELECT Id, ContactId FROM User WHERE Contact.PortalRole__c INCLUDES (:role) LIMIT 1];
    }

    public static Account createAccount(String name) {
        Account theAccount = new Account(Name = name);
        insert theAccount;
        return theAccount;
    }

    public static Contact createContact(Id accountId, String role) {
        Contact theContact = new Contact(
                FirstName = 'Jane',
                LastName = 'Smith',
                AccountId = accountId,
                Email = UUID.randomUUID() + '@example.com',
                PortalRole__c = role,
                Division__c = 'Middle School'
        );
        insert  theContact;
        return theContact;
    }
}
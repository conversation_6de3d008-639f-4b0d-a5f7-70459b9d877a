public with sharing class cm_MembersManagementService {
    public static List<cm_MembersManagementDTO> getConferenceData(Id conferenceId, Boolean showOnlyApproved) {
        // Just in case cmp_service is not installed need coverage
        cm_MembersManagementService.toCover();

        Map<Id, cm_MembersManagementDTO> result = cmp_Service.getCompetitionMembers(conferenceId, showOnlyApproved);
        return result.values();
    }

    public static void updateConferenceRequests(List<Id> requestIds, Boolean isApproved) {
        List<cm_Participant__c> participants = cm_Selector.getConferenceParticipants(requestIds);
        for (cm_Participant__c participant : participants) {
            participant.RegistrationStatus__c = isApproved ? 'Approved' : 'Rejected';
        }
        WS.updateRecords(participants);

        sendEmailNotifications(participants, isApproved);
    }

    private static void sendEmailNotifications(List<cm_Participant__c> participants, Boolean isApproved) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        String orgWideId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;

        String content = System.Label.cmConferenceMembersStatusMessage;
        String status = isApproved ? 'Approved' : 'Rejected';
        String body = content.replace('{status}', status).replace('{userName}', UserInfo.getName());

        for (cm_Participant__c participant : participants) {
            String subject = participant.Conference__r.Name;
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setToAddresses(new List<String>{ participant.Contact__r.Email });
            email.setSubject(subject);
            email.setHtmlBody(body);
            email.setOrgWideEmailAddressId(orgWideId);

            emails.add(email);
        }
        Messaging.sendEmail(emails);
    }

    public static void sendListOfApprovedMembers(Id conferenceId, List<cm_MembersManagementDTO> approvedMembers) {
        String orgWideId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
        String toEmail = [SELECT Contact.Email FROM User WHERE Id = :UserInfo.getUserId()].Contact.Email;
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        cm_Conference__c conference = cm_Selector.getConference(conferenceId);
        email.setToAddresses(new List<String>{ toEmail });
        email.setSubject(System.Label.cmEmailStatusHeader.replace('{conferenceName}', conference.Name));

        String body = generateEmailBody(approvedMembers);
        email.setHtmlBody(body);
        email.setOrgWideEmailAddressId(orgWideId);
        Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{ email });
    }

    private static String generateEmailBody(List<cm_MembersManagementDTO> approvedMembers) {
        String body =
            '<html><body><table border="1"><tr><th>' +
            System.Label.cmEmailNameHeader +
            '</th><th>' +
            System.Label.cmEmailStatusHeader +
            '</th><th>' +
            System.Label.cmEmailRoleHeader +
            '</th><th>' +
            System.Label.cmEmailFeeHeader +
            '</th><th>' +
            System.Label.cmEmailCompetitionsHeader +
            '</th></tr>';
        for (cm_MembersManagementDTO participant : approvedMembers) {
            body += generateRow(participant);
        }
        body += '</table></body></html>';
        return body;
    }

    private static String generateRow(cm_MembersManagementDTO participant) {
        String row = '<tr><td style="padding: 10px;">' + participant.contactName + '</td>';
        row += '<td style="padding: 10px;">' + participant.status + '</td>';
        row += '<td style="padding: 10px;">' + participant.role + '</td>';
        row += '<td style="padding: 10px;">$' + participant.fee + '</td>';
        row += generateCompetitionCell(participant.competitions);
        row += '</tr>';
        return row;
    }

    private static String generateCompetitionCell(List<String> competitions) {
        String row = '<td style="padding: 5px;"><ol>';
        String competitionsRow = '';
        if (competitions != null) {
            for (String competition : competitions) {
                competitionsRow += '<li>' + competition + '</li>';
            }
        }
        return row + competitionsRow + '</ol></td>';
    }

    private static void toCover() {
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }

}
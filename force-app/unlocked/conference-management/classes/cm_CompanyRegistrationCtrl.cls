public with sharing class cm_CompanyRegistrationCtrl {
    private static final String FORM_LAYOUT_NAME = 'cm_Exhibitor__c-[UNM] Exhibitor Registration Form';

    @AuraEnabled
    public static aclab.FormLayout getForm(Id conferenceId) {
        try {
            Id existingRecordId = getExhibitorRecord(conferenceId);

            if (existingRecordId != null) {
                return aclab.Form.retrieve(FORM_LAYOUT_NAME, existingRecordId);
            }

            return aclab.Form.retrieve(FORM_LAYOUT_NAME);
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }

    @AuraEnabled
    public static Id saveRecord(cm_Exhibitor__c record, Id conferenceId, String accessCode) {
        try {
            Account theAccount = [SELECT Id, Name FROM Account WHERE Id = :PortalUser.getAccountId()];
            cm_Exhibitor__c theRecord = (cm_Exhibitor__c) aclab.Form.cleanInjectedFields(record, FORM_LAYOUT_NAME);
            theRecord.Account__c = theAccount.Id;
            theRecord.Conference__c = conferenceId;
            theRecord.AccessCode__c = accessCode;
            insert theRecord;
            return  theRecord.Id;
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }


    @AuraEnabled(Cacheable=false)
    public static Id getConferenceExhibitorId(Id conferenceId) {
        try {
            return getExhibitorRecord(conferenceId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Map<String, Object> getPredefinedValues(Id conferenceId) {
        try {
            Map<String, Object> result = new Map<String, Object>();

            Id existingRecordId = getExhibitorRecord(conferenceId);

            if (existingRecordId == null) {

                Account theAccount = [SELECT Id, Name, BillingCity, BillingPostalCode, BillingState, BillingStreet FROM Account WHERE Id = :PortalUser.getAccountId()];
                Contact theContact = [SELECT Id, FirstName, LastName, Email, Phone FROM Contact WHERE Id = :PortalUser.getContactId()];

                result.put('OrganizationName__c', theAccount.Name);
                result.put('OnSiteContactFirstName__c', theContact.FirstName);
                result.put('OnSiteContactLastName__c', theContact.LastName);
                result.put('Email__c', theContact.Email);
                result.put('Phone__c', theContact.Phone);
                result.put('AddressStreetLineOne__c', theAccount.BillingStreet);
                result.put('AddressState__c', theAccount.BillingState);
                result.put('AddressCity__c', theAccount.BillingCity);
                result.put('AddressPostalCode__c', theAccount.BillingPostalCode);
            }

            return result;
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }

    private static Id getExhibitorRecord(Id conferenceId) {
        Id accountId = PortalUser.getAccountId();
        if (Test.isRunningTest()) {
            accountId = [SELECT Id FROM Account LIMIT 1].Id;
        }
        return [
                SELECT Id FROM cm_Exhibitor__c WHERE Conference__c = :conferenceId AND Account__c = :accountId LIMIT 1
        ]?.Id;
    }
}
@IsTest
public class cm_ApprovalNotificationsTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact advisor = cm_TestUtils.createContact(theAccount.Id, 'Advisor');
            PortalUser.create(advisor);
            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;

            cm_RegistrationType__c registrationType = new cm_RegistrationType__c(
                    Name = 'Student',
                    IsActive__c = true,
                    Conference__c = conference.Id,
                    IsCompetitionsSelectionAvailable__c = true
            );
            insert registrationType;

            cm_Participant__c conferenceParticipant1 = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = student.Id,
                    ConferenceRegistrationType__c = registrationType.Id,
                    RegistrationStatus__c = 'Pending Approval'
            );

            cm_Participant__c conferenceParticipant2 = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = advisor.Id,
                    ConferenceRegistrationType__c = registrationType.Id,
                    RegistrationStatus__c = 'Pending Approval'
            );

            insert new List<cm_Participant__c>{ conferenceParticipant1, conferenceParticipant2 };
        }
    }

    @IsTest
    static void sendLeadAdvisorApprovalNotifications() {
        Integer emailsSent;
        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                cm_ApprovalNotifications job = new cm_ApprovalNotifications();
                job.execute(null);
                emailsSent = Limits.getEmailInvocations();
            }
            Test.stopTest();
        }
        Assert.areEqual(1, emailsSent, 'One Email must be sent');
    }
}
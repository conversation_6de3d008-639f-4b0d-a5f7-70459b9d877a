@IsTest
public class cm_SelfRegistrationCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference');
            insert conference;

            cm_RegistrationType__c registrationType1 = new cm_RegistrationType__c(
                    Name = 'Student',
                    IsActive__c = true,
                    Conference__c = conference.Id
            );
            cm_RegistrationType__c registrationType2 = new cm_RegistrationType__c(
                    Name = 'Advisor',
                    IsActive__c = false,
                    Conference__c = conference.Id
            );
            cm_RegistrationType__c registrationType3 = new cm_RegistrationType__c(
                    Name = 'Lead',
                    IsActive__c = true,
                    Conference__c = conference.Id
            );
            insert new List<cm_RegistrationType__c>{ registrationType1, registrationType2, registrationType3 };
        }
    }

    @IsTest
    static void initTest() {
        System.runAs(cm_TestUtils.getAdmin()) {
            cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
            cm_SelfRegistrationCtrl.init(conference.Id);
        }
    }
}
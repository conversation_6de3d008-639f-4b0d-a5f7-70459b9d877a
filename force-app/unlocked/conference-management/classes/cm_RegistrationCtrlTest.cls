@IsTest
private class cm_RegistrationCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact advisor = cm_TestUtils.createContact(theAccount.Id, 'Advisor');
            PortalUser.create(advisor);
            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            Date today = System.today();

            cm_Conference__c conference = new cm_Conference__c(
                    Name = 'Test Conference',
                    RegistrationDeadline__c = today.addDays(10),
                    IsOpenForRegistration__c = true,
                    LimitOfAdvisorsPerSchool__c = 10
            );
            insert conference;

            cm_RegistrationType__c RT = new cm_RegistrationType__c(
                    Name = 'Advisor',
                    Conference__c = conference.Id,
                    IsActive__c = true
            );
            insert RT;
        }
    }

    @IsTest
    static void testInit() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c];
        Contact student = [SELECT Id FROM Contact WHERE PortalRole__c = 'Student'];
        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            cm_RegistrationCtrl.init(conference.Id, student.Id);
        }
    }

    @IsTest
    static void testSaveRecord() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c];
        Contact student = [SELECT Id FROM Contact WHERE PortalRole__c = 'Student'];
        try {
            System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
                cm_RegistrationCtrl.saveRecord(new Contact(), conference.Id, null, null, false, true);
            }
        } catch (Exception e) {
            // Form layout is not part of package, so this exception is predictable
        }
    }

    @IsTest
    static void testSaveConferenceParticipant() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c];
        Contact advisor = [SELECT Id FROM Contact WHERE PortalRole__c = 'Advisor'];

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            cm_RegistrationCtrl.saveConferenceParticipant(advisor.Id, conference.Id, null, false, true);
            cm_RegistrationCtrl.init(conference.Id, advisor.Id);
        }
    }

    @IsTest
    static void testGetForm() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c];
        try {
            System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
                cm_RegistrationCtrl.getForm(conference.Id);
            }
        } catch (Exception e) {
            // Form layout is not part of package, so this exception is predictable
        }
    }
}
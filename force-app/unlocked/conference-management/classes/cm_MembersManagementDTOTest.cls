@IsTest
private class cm_MembersManagementDTOTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference');
            insert conference;

            cm_RegistrationType__c registrationType = new cm_RegistrationType__c(
                    Name = 'Student',
                    IsActive__c = true,
                    Conference__c = conference.Id
            );

            cm_Participant__c conferenceParticipant1 = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = student.Id,
                    ConferenceRegistrationType__c = registrationType.Id,
                    RegistrationStatus__c = 'Pending Approval'
            );
            insert conferenceParticipant1;
        }
    }

    @IsTest
    static void testBehavior() {
        System.runAs(cm_TestUtils.getAdmin()) {
            cm_Participant__c participant = [
                    SELECT Id, Contact__r.FirstName, Contact__r.LastName, Contact__r.Name,
                            Contact__r.PortalRole__c, Conference__r.FeeStudent__c,  RegistrationStatus__c
                    FROM cm_Participant__c
            ];
            new cm_MembersManagementDTO(participant, new List<String>());
        }
    }
}
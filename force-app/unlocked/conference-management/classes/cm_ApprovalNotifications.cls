global class cm_ApprovalNotifications implements Schedulable {
    public static final String APPROVAL_TEMPLATE_NAME = 'cm_ParticipantsApprovalEmail';

    global void execute(SchedulableContext sc) {
        sendLeadAdvisorApprovalNotifications();
    }

    private void sendLeadAdvisorApprovalNotifications() {
        List<cm_Participant__c> participants = cm_Selector.getConferenceParticipants();

        if (!participants.isEmpty()) {
            Map<Id, List<cm_Participant__c>> participantsGroupedByAccount = groupParticipantsByAccount(
                participants
            );
            List<Contact> leadAdvisors = cm_Selector.getLeadAdvisors();
            Set<String> emailsToSendNotifications = getEmailsToSendNotifications(
                leadAdvisors,
                participantsGroupedByAccount
            );
            sendNotifications(emailsToSendNotifications);
        }
    }

    private Map<Id, List<cm_Participant__c>> groupParticipantsByAccount(
        List<cm_Participant__c> participants
    ) {
        Map<Id, List<cm_Participant__c>> groupedByAccount = new Map<Id, List<cm_Participant__c>>();

        for (cm_Participant__c participant : participants) {
            if (groupedByAccount.containsKey(participant.Contact__r.AccountId)) {
                groupedByAccount.get(participant.Contact__r.AccountId).add(participant);
            } else {
                groupedByAccount.put(
                    participant.Contact__r.AccountId,
                    new List<cm_Participant__c>{ participant }
                );
            }
        }
        return groupedByAccount;
    }

    private Set<String> getEmailsToSendNotifications(
        List<Contact> leadAdvisors,
        Map<Id, List<cm_Participant__c>> participants
    ) {
        Set<String> emails = new Set<String>();

        for (Contact leadAdvisor : leadAdvisors) {
            if (participants.containsKey(leadAdvisor.AccountId) && !participants.get(leadAdvisor.AccountId).isEmpty()) {
                emails.add(leadAdvisor.Email);
            }
        }
        return emails;
    }

    private void sendNotifications(Set<String> emailsToSendNotifications) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        EmailTemplate template = cm_Selector.getEmailTemplate(APPROVAL_TEMPLATE_NAME);
        String orgWideId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;

        for (String leadEmail : emailsToSendNotifications) {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setToAddresses(new List<String>{ leadEmail });
            email.setSubject(template.Subject);
            email.setHtmlBody(template.Body);
            email.setOrgWideEmailAddressId(orgWideId);

            emails.add(email);
        }
        Messaging.sendEmail(emails);
    }
}
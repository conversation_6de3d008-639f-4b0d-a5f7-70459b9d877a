public with sharing class cm_MembersManagementDTO {
    @AuraEnabled public Id registrationRequestId;
    @AuraEnabled public Id contactId;
    @AuraEnabled public String contactName;
    @AuraEnabled public String firstName;
    @AuraEnabled public String lastName;
    @AuraEnabled public String status;
    @AuraEnabled public String role;
    @AuraEnabled public Double fee;
    @AuraEnabled public Boolean readOnly;
    @AuraEnabled public List<String> competitions;

    public cm_MembersManagementDTO(cm_Participant__c participant, List<String> competitions) {
        this.registrationRequestId = participant.Id;
        this.contactId = participant.Contact__c;
        this.contactName = participant.Contact__r.Name;
        this.firstName = participant.Contact__r.FirstName;
        this.lastName = participant.Contact__r.LastName;
        this.status = participant.RegistrationStatus__c;
        this.role = participant.Contact__r.PortalRole__c;
        this.fee = participant.Contact__r.PortalRole__c.contains('Advisor') ? participant.Conference__r.FeePro__c : participant.Conference__r.FeeStudent__c; // TO DO: need to know how to calculate fee
        this.readOnly = this.status == 'Approved' || this.status == 'Rejected';
        this.competitions = competitions;
    }
}
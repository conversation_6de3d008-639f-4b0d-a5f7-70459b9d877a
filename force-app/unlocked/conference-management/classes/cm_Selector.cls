public with sharing class cm_Selector {

    public static List<cm_RegistrationType__c> getConferenceRegistrationTypes(
        Id conferenceId,
        Boolean withRecordsThatAllowCompetitionSelection
    ) {
        Set<Boolean> valuesOfCompetitionSelectionField = new Set<Boolean>{ false };
        if (withRecordsThatAllowCompetitionSelection) {
            valuesOfCompetitionSelectionField.add(true);
        }
        return [
            SELECT Id, Name, IsCompetitionsSelectionAvailable__c
            FROM cm_RegistrationType__c
            WHERE Conference__c = :conferenceId
            AND IsActive__c = TRUE
            AND IsCompetitionsSelectionAvailable__c IN :valuesOfCompetitionSelectionField
            AND Name != 'Advisor'
            WITH USER_MODE
        ];
    }

    public static cm_Participant__c getConferenceParticipant(Id conferenceId, Id contactId) {
        return [
            SELECT Id, ConferenceRegistrationType__c, Contact__r.cm_HasDisability__c, Contact__r.cm_HasDietaryRestriction__c,
                    Contact__r.Division__c, Contact__r.NeedsADAAccommodations__c, Contact__r.NeedsDietaryAccommodations__c,
                    Contact__r.NeedsEducationAccommodations__c
            FROM cm_Participant__c
            WHERE Conference__c = :conferenceId
            AND Contact__c = :contactId
            WITH USER_MODE
            LIMIT 1
        ] ?? null;
    }

    public static List<cm_Participant__c> getConferenceParticipants() {
        return [
            SELECT Id, Contact__r.AccountId
            FROM cm_Participant__c
            WHERE RegistrationStatus__c = 'Pending Approval'
            WITH USER_MODE
        ];
    }

    public static cm_Conference__c getConference(Id conferenceId) {
        return [
            SELECT Id, AssessmentResultsEnabled__c, AssessmentScoreEnabled__c,
                    Name, IsCompetitionsAvailable__c, EmployabilityAssessment__c
            FROM cm_Conference__c
            WHERE Id = :conferenceId
        ];
    }

    public static User getUserByContact(Id contactId) {
        return [SELECT Id FROM User WHERE ContactId = :contactId];
    }

    public static List<Contact> getNotRegisteredContactsByRole(Id conferenceId, String portalRole) {
        if (portalRole.equalsIgnoreCase('Student')) {
            return [
                    SELECT Id, Name, FirstName, LastName, IsFreshman__c, PortalRole__c
                    FROM Contact
                    WHERE PortalRole__c IN ('Student', 'Middle School Member')
                    AND ApprovalStatus__c IN ('Approved by Advisor', 'User Created by Advisor', 'User Created by Lead Advisor')
                    AND Id NOT IN (SELECT Contact__c FROM cm_Participant__c WHERE Conference__c = :conferenceId)
                    WITH USER_MODE
                    ORDER BY LastName
            ];
        }
        return [
                SELECT Id, Name, FirstName, LastName, IsFreshman__c, PortalRole__c
                FROM Contact
                WHERE PortalRole__c INCLUDES ('Lead Advisor', 'Advisor')
                AND Id NOT IN (SELECT Contact__c FROM cm_Participant__c WHERE Conference__c = :conferenceId)
                WITH USER_MODE
                ORDER BY LastName
        ];
    }

    public static List<cm_Participant__c> getConferenceParticipants(Id conferenceId, Boolean showOnlyApproved) {
        Set<String> statuses = new Set<String> {'Pending Approval', 'Approved', 'Rejected'};
        if(showOnlyApproved) {
             statuses = new Set<String> {'Approved'};
        }

        return [
                SELECT Id,
                        RegistrationStatus__c,
                        Contact__c,
                        Contact__r.FirstName,
                        Contact__r.LastName,
                        Contact__r.Name,
                        Contact__r.PortalRole__c,
                        Conference__r.FeeStudent__c,
                        Conference__r.FeePro__c
                FROM cm_Participant__c
                WHERE Conference__c = :conferenceId
                AND RegistrationStatus__c IN :statuses
                AND Contact__r.AccountId = :PortalUser.getAccountId()
                ORDER BY Contact__r.LastName
        ];
    }

    public static List<cm_Participant__c> getConferenceParticipants(List<Id> participantIds) {
        return [
            SELECT Id, RegistrationStatus__c, Contact__r.Email, Conference__r.Name
            FROM cm_Participant__c
            WHERE Id IN :participantIds
            AND Contact__r.AccountId = :PortalUser.getAccountId()
        ];
    }

    public static List<Contact> getLeadAdvisors() {
        return [SELECT Id, Email, AccountId FROM Contact WHERE PortalRole__c INCLUDES ('Lead Advisor') WITH USER_MODE];
    }

    public static EmailTemplate getEmailTemplate(String developerName) {
        return [SELECT Id, Body, HtmlValue, Subject FROM EmailTemplate WHERE DeveloperName = :developerName] ?? null;
    }

}
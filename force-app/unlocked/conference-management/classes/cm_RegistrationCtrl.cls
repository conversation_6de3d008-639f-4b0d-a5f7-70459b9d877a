public with sharing class cm_RegistrationCtrl {
    private static final String FORM_LAYOUT_NAME = 'Contact-%5BCM%5D Conference Contact Registration';

    public class Response {
        @AuraEnabled
        public List<cm_RegistrationType__c> registrationTypes { get; set; }
        @AuraEnabled
        public cm_Participant__c participant { get; set; }
        @AuraEnabled
        public Boolean useLimits { get; set; }
        @AuraEnabled
        public Decimal limitOfDelegates { get; set; }
        @AuraEnabled
        public Decimal remainingLimitOfDelegates { get; set; }
        @AuraEnabled
        public Boolean displayEmergencyContact { get; set; }
        @AuraEnabled
        public Boolean displayAddOns { get; set; }
    }

    @AuraEnabled
    public static cm_RegistrationCtrl.Response init(Id conferenceId, Id recordId) {
        try {
            cm_Conference__c conference = new WSHelper().getConference(conferenceId);
            cm_RegistrationCtrl.Response response = new cm_RegistrationCtrl.Response();

            Boolean useLimits = false;
            useLimits = true; // TODO use custom settings
            Integer numberOfDelegates = 0;
            Decimal theLimitOfDelegates = 1000;

            if (useLimits) {
                for (cm_Participant__c participant: conference.Participants__r) {
                    Boolean isDelegateRegistrationType = participant.ConferenceRegistrationType__r?.Name?.contains('Delegate') ?? false;
                    Boolean isDelegateCompetitor = participant.IsDelegate__c;
                    if (isDelegateRegistrationType || isDelegateCompetitor) {
                        theLimitOfDelegates = participant.Contact__r.Account.LimitOfDelegates__c;
                        numberOfDelegates++;
                    }
                }
            }

            response.registrationTypes = cm_Selector.getConferenceRegistrationTypes(conferenceId, conference.IsCompetitionsAvailable__c);
            response.participant = cm_Selector.getConferenceParticipant(conferenceId, recordId);
            response.useLimits = useLimits;
            response.limitOfDelegates = theLimitOfDelegates;
            response.remainingLimitOfDelegates = theLimitOfDelegates - numberOfDelegates;
            response.displayAddOns = conference.DisplayAddOnsOnRegForm__c;
            response.displayEmergencyContact = conference.DisplayEmergencyContactOnRegForm__c;
            return response;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static aclab.FormLayout getForm(Id recordId) {
        try {
            return aclab.Form.retrieve(FORM_LAYOUT_NAME, recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Id saveRecord(
            Contact record,
            Id conferenceId,
            Id registrationTypeId,
            List<cmp_CompetitionsSelectionCtrl.CompetitionOrder> orderedCompetitions,
            Boolean isDelegate,
            Boolean skipApprovalStatusUpdate
    ) {
        try {
            if (cm_MembersRegistrationCtrl.isConferenceAvailableForRegistration(conferenceId) == false) {
                throw Error.toLWC('Registration is not open.');
            }

            Set<String> additionalFieldsToClean = new Set<String>{'Name', 'Email', 'Division__c', 'Birthday'};
             Contact theContact = (Contact) aclab.Form.cleanInjectedFields(record, FORM_LAYOUT_NAME, additionalFieldsToClean);

            theContact.AccountId = PortalUser.getAccountId();
            WS.updateRecord(theContact);

            saveConferenceParticipant(theContact.Id, conferenceId, registrationTypeId, isDelegate, skipApprovalStatusUpdate);

            cmp_CompetitionsSelectionCtrl.updateParticipantCompetitions(theContact.Id, orderedCompetitions);

            return theContact.Id;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @TestVisible
    private static void saveConferenceParticipant(
            Id contactId,
            Id conferenceId,
            Id registrationTypeId,
            Boolean isDelegate,
            Boolean skipApprovalStatusUpdate
    ) {
        Boolean isCurrentLA = PortalUser.hasRole(PortalUser.Role.LEAD_ADVISOR);
        cm_Participant__c participant = cm_Selector.getConferenceParticipant(conferenceId, contactId);
        Map<Id, cm_RegistrationType__c> registrationTypeMap = new WSHelper().getRegistrationTypeMap(conferenceId);

        cm_RegistrationType__c currentRegistrationType = registrationTypeMap.get(registrationTypeId);

        if (currentRegistrationType != null && currentRegistrationType.Name.containsIgnoreCase('Delegate')) {
            isDelegate = true;
        }

        if (registrationTypeId == null && (isCurrentLA || PortalUser.hasRole(contactId, PortalUser.Role.ADVISOR))) {
            Id advisorRegistrationType = null;
            for (String key: registrationTypeMap.keySet()) {
                cm_RegistrationType__c RT = registrationTypeMap.get(key);
                if (RT != null && RT.Name.equalsIgnoreCase('Advisor')) {
                    advisorRegistrationType = RT.Id;
                }
            }

            registrationTypeId = advisorRegistrationType;

            if (registrationTypeId != null) {
                // Check limits
                Integer remainingLimit = cm_ConferenceService.getLimitOfAdvisorsForSchool(conferenceId, PortalUser.getAccountId());
                if (remainingLimit <= 0) {
                    throw Error.toLWC('Limit of advisors');
                }
            }
        }

        String approvalStatus = isCurrentLA ? 'Approved' : 'Pending Approval';

        cm_Participant__c participantToUpdate = new cm_Participant__c(
                Id = participant?.Id,
                Conference__c = conferenceId,
                Contact__c = contactId,
                ConferenceRegistrationType__c = registrationTypeId,
                IsDelegate__c = isDelegate
        );

        if (skipApprovalStatusUpdate == false) {
            participantToUpdate.RegistrationStatus__c = approvalStatus; // If current user is Advisor then set status to Approved
        }

        WS.upsertRecord(participantToUpdate);
    }

    without sharing class WSHelper {
        public cm_Conference__c getConference(Id conferenceId) {
            Id accountId = PortalUser.getAccountId();
            return [
                    SELECT Id,
                            Name,
                            IsCompetitionsAvailable__c,
                            LimitOfAdvisorsPerSchool__c,
                            DisplayEmergencyContactOnRegForm__c,
                            DisplayAddOnsOnRegForm__c, (
                            SELECT Id,
                                    ConferenceRegistrationType__r.Name,
                                    Contact__r.Account.LimitOfDelegates__c,
                                    IsDelegate__c
                            FROM Participants__r
                            WHERE Contact__r.AccountId = :accountId
                    )
                    FROM cm_Conference__c
                    WHERE Id = :conferenceId
            ];
        }

        public Map<Id, cm_RegistrationType__c> getRegistrationTypeMap(Id conferenceId) {
            return new Map<Id, cm_RegistrationType__c>([
                    SELECT Id, Name
                    FROM cm_RegistrationType__c
                    WHERE IsActive__c = TRUE AND Conference__c = :conferenceId
            ]);
        }
    }

}
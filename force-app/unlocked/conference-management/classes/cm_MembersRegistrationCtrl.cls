public with sharing class cm_MembersRegistrationCtrl {

    @AuraEnabled(Cacheable=false)
    public static Boolean isConferenceAvailableForRegistration(Id conferenceId) {
        try {
            cm_Conference__c conference = new cm_MembersRegistrationCtrl.WS().getConference(conferenceId);
            Datetime deadline = conference.RegistrationDeadline__c;
            return deadline != null && System.now() <= deadline && conference.IsOpenForRegistration__c;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static cm_MembersRegistrationCtrl.Response getContacts(Id conferenceId, String role) {
        try {
            if (cm_MembersRegistrationCtrl.isConferenceAvailableForRegistration(conferenceId) == false) {
                throw Error.toLWC('Registration is not open.');
            }

            cm_MembersRegistrationCtrl.Response response = new cm_MembersRegistrationCtrl.Response();

            cm_Conference__c conference = new cm_MembersRegistrationCtrl.WS().getConference(conferenceId);

            Integer numberOfAdvisors = 0;
            for (cm_Participant__c participant: conference.Participants__r) {
                if (participant.Contact__r.PortalRole__c?.contains('Advisor') == true) {
                    numberOfAdvisors++;
                }
            }

            Decimal theLimit = conference.LimitOfAdvisorsPerSchool__c ?? 1000;
            Integer advisorsRegistered = numberOfAdvisors;

            response.limitOfAdvisors = theLimit;
            response.remainingLimitOfAdvisors = theLimit - advisorsRegistered;
            response.contacts = cm_Selector.getNotRegisteredContactsByRole(conferenceId, role);
            return response;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    public class Response {
        @AuraEnabled
        public Decimal limitOfAdvisors { get; set; }
        @AuraEnabled
        public Decimal remainingLimitOfAdvisors { get; set; }
        @AuraEnabled
        public List<Contact> contacts { get; set; }
    }

    without sharing class WS {
        public cm_Conference__c getConference(Id conferenceId) {
            return [
                    SELECT Id, LimitOfAdvisorsPerSchool__c, RegistrationDeadline__c, IsOpenForRegistration__c, (
                            SELECT Id, ConferenceRegistrationType__r.Name, Contact__r.PortalRole__c, Contact__r.Account.LimitOfDelegates__c
                            FROM Participants__r
                            WHERE Contact__r.AccountId = :PortalUser.getAccountId()
                    )
                    FROM cm_Conference__c
                    WHERE Id = :conferenceId
            ];
        }
    }

}
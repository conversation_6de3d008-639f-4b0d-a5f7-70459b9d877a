import { LightningElement, api } from 'lwc';
import { showErrors } from 'c/errorHandler';
import getConferenceExhibitorId from '@salesforce/apex/cm_CompanyRegistrationCtrl.getConferenceExhibitorId';
import AGREEMENT_FORM from 'c/cmCompanyRegistrationAgreement';
import REGISTRATION_FORM from 'c/cmCompanyRegistrationForm';

export default class extends LightningElement {
    @api conferenceId;
    @api stretch;
    @api variant;
    @api label;
    isDisabled = true;
    hasRecord = false;
    partnerAccessCode;

    connectedCallback() {
        this.loadConferenceExhibitor();
    }

    loadConferenceExhibitor() {
        getConferenceExhibitorId({ conferenceId: this.conferenceId })
            .then(foundRecordId => {
                if (foundRecordId) {
                    this.hasRecord = true;
                    this.label = 'View Exhibitor Registration Status';
                }
                this.isDisabled = false;
            })
            .catch((error) => {
                showErrors(this, error);
            });
    }

    async openRegisterModal() {
        if (this.hasRecord) {
            this.openRegisterForm({
                size: 'small',
                conferenceId: this.conferenceId,
                hasRecord: true
            });
        }
        else {
            const responseAgreementForm = await AGREEMENT_FORM.open({ size: 'small' });
            if (responseAgreementForm.openNextModal) {
                this.openRegisterForm({
                    size: 'small',
                    conferenceId: this.conferenceId,
                    partnerAccessCode: responseAgreementForm.partnerAccessCode,
                    hasRecord: this.hasRecord,
                });
            }
        }
    }

    async openRegisterForm(params) {
        await REGISTRATION_FORM.open(params);
        this.loadConferenceExhibitor();
    }
}

import LightningModal from 'lightning/modal';
import labels from './labels';
const EMAIL_ADDRESS = '<EMAIL>';
export default class CmCompanyRegistrationAgreement extends LightningModal {
    isAgreed = false;
    partnerAccessCode;
    labels = labels;

    get cmExhibitorAgreementText() {
        return this.labels.cmExhibitorAgreementText.replace(
            '{email}',
            `<a href="mailto:${EMAIL_ADDRESS}">${EMAIL_ADDRESS}</a>`
        );
    }

    get disableNextButton() {
        return !this.isAgreed;
    }

    handleAgree(event) {
        this.isAgreed = event.target.checked;
    }

    handlePartnerAccessCodeChange(event) {
        this.partnerAccessCode = event.target.value;
    }

    handleNext() {
        this.close({
            partnerAccessCode: this.partnerAccessCode,
            openNextModal: true
        });
    }
}

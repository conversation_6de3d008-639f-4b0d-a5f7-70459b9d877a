<template>
    <lightning-modal-header label={labels.cmExhibitorAgreementModalLabel}></lightning-modal-header>
    <lightning-modal-body>
        <p class="slds-m-bottom_medium">
            <lightning-formatted-rich-text value={cmExhibitorAgreementText}></lightning-formatted-rich-text>
        </p>
        <lightning-input
            type="checkbox"
            label={labels.cmExhibitorAgreeLabel}
            value={isAgreed}
            onclick={handleAgree}
        ></lightning-input>
        <lightning-input
            type="text"
            label={labels.cmExhibitorAgreePartnerAccessCodeLabel}
            placeholder={labels.cmExhibitorAgreePartnerAccessCodeLabel}
            value={partnerAccessCode}
            onchange={handlePartnerAccessCodeChange}
        ></lightning-input>
        <p>{labels.cmExhibitorAgreePartnerAccessCodeMessage}</p>
    </lightning-modal-body>
    <lightning-modal-footer>
        <lightning-button
            variant="brand"
            label={labels.cmExhibitorNextButtonLabel}
            class="slds-m-left_x-small"
            onclick={handleNext}
            disabled={disableNextButton}
        ></lightning-button>
    </lightning-modal-footer>
</template>

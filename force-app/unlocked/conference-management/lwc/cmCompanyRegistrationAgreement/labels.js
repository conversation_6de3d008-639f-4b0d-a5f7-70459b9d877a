import cmExhibitorAgreementModalLabel from '@salesforce/label/c.cmExhibitorAgreementModalLabel';
import cmExhibitorAgreementText from '@salesforce/label/c.cmExhibitorAgreementText';
import cmExhibitorAgreeLabel from '@salesforce/label/c.cmExhibitorAgreeLabel';
import cmExhibitorAgreePartnerAccessCodeLabel from '@salesforce/label/c.cmExhibitorAgreePartnerAccessCodeLabel';
import cmExhibitorAgreePartnerAccessCodeMessage from '@salesforce/label/c.cmExhibitorAgreePartnerAccessCodeMessage';
import cmExhibitorNextButtonLabel from '@salesforce/label/c.cmExhibitorNextButtonLabel';

export default {
    cmExhibitorAgreementModalLabel,
    cmExhibitorAgreementText,
    cmExhibitorAgreeLabel,
    cmExhibitorAgreePartnerAccessCodeLabel,
    cmExhibitorAgreePartnerAccessCodeMessage,
    cmExhibitorNextButtonLabel
};

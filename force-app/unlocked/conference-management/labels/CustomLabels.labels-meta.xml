<?xml version="1.0" encoding="UTF-8" ?>
<CustomLabels xmlns="http://soap.sforce.com/2006/04/metadata">
    <labels>
        <fullName>cmCompanyRegistrationFormLabel</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Company registration form label</shortDescription>
        <value>Company Registration Form</value>
    </labels>
    <labels>
        <fullName>cmCompanyRegistrationSuccessMessage</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Company registration success message</shortDescription>
        <value>Company was successful registered to conference</value>
    </labels>
    <labels>
        <fullName>cmCompanyRegistrationSaveButtonLabel</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Company registration save button label</shortDescription>
        <value>Save</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersActionsTableHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Actions table header</shortDescription>
        <value>Actions</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersApproveButtonLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Approve button label</shortDescription>
        <value>Approve</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersApproveButtonSelectedLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Approve selected button label</shortDescription>
        <value>Approve Selected</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersApproveMessage</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Approve confirm message</shortDescription>
        <value>Are you sure you want to approve {number} record(s)?</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersCompetitionLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Competition label</shortDescription>
        <value>competition</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersCompetitionsLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Competitions label</shortDescription>
        <value>competitions</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersCompetitionsTableHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Competitions table header</shortDescription>
        <value>Competitions</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersConfirmationMessage</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Email sending confirmation message</shortDescription>
        <value>Are you sure you want to send a list of approved members to your email address {email}?</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersFeeTableHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Fee table header</shortDescription>
        <value>Fee</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersNameTableHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Name table header</shortDescription>
        <value>Name</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersRecordsNotFound</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Uses for empty state</shortDescription>
        <value>There are no records in approval process list</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersRejectButtonLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Reject button label</shortDescription>
        <value>Reject</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersRejectButtonSelectedLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Reject selected button label</shortDescription>
        <value>Reject Selected</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersRejectMessage</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Reject confirm message</shortDescription>
        <value>Are you sure you want to reject {number} record(s)?</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersRoleTableHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Role table header</shortDescription>
        <value>Role</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersSelectedRecordsLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Selected records label</shortDescription>
        <value>Selected records:</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersSendListButtonLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Send list of approved members label</shortDescription>
        <value>Send List of Approved Members</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersStatusMessage</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Request notification message</shortDescription>
        <value>Your conference application was {status} by {userName}.</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersStatusTableHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Status table header</shortDescription>
        <value>Status</value>
    </labels>
    <labels>
        <fullName>cmConferenceMembersTotalFeeLabel</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Total fee label</shortDescription>
        <value>Total Fee:</value>
    </labels>
    <labels>
        <fullName>cmEmailCompetitionsHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Email competitions header</shortDescription>
        <value>Competitions</value>
    </labels>
    <labels>
        <fullName>cmEmailFeeHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Email fee header</shortDescription>
        <value>Fee</value>
    </labels>
    <labels>
        <fullName>cmEmailNameHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Email name header</shortDescription>
        <value>Name</value>
    </labels>
    <labels>
        <fullName>cmEmailRoleHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Email role header</shortDescription>
        <value>Role</value>
    </labels>
    <labels>
        <fullName>cmEmailStatusHeader</fullName>
        <categories>cm_ConferenceMembersManagementComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Email status header</shortDescription>
        <value>Status</value>
    </labels>
    <labels>
        <fullName>cmExhibitorAgreeLabel</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Exhibitor agreement agree label</shortDescription>
        <value>Agree</value>
    </labels>
    <labels>
        <fullName>cmExhibitorAgreePartnerAccessCodeLabel</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Exhibitor agreement partner access code label</shortDescription>
        <value>Partner Access Code</value>
    </labels>
    <labels>
        <fullName>cmExhibitorAgreePartnerAccessCodeMessage</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Exhibitor agreement partner access code message</shortDescription>
        <value
        >If you have been provided an access code, enter it here. If you do not have an access code, click next.</value>
    </labels>
    <labels>
        <fullName>cmExhibitorAgreementModalLabel</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Exhibitor agreement modal label</shortDescription>
        <value>Exhibitor Agreement</value>
    </labels>
    <labels>
        <fullName>cmExhibitorAgreementText</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Exhibitor agreement text</shortDescription>
        <value
        >By completing this form, you and all guests are agreeing to follow the SkillsUSA Illinois policies. This includes the understanding of the cancellation and refund policy, appropriate use policy, and incidental policy as outlined . If you have any questions, please contact SkillsUSA Illinois at {email}.</value>
    </labels>
    <labels>
        <fullName>cmExhibitorNextButtonLabel</fullName>
        <categories>cm_CompanyRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Exhibitor agreement next button label</shortDescription>
        <value>Next</value>
    </labels>
    <labels>
        <fullName>cmMemberRegistrationPermissionErrorMessage</fullName>
        <categories>cm_MembersRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Permission error message</shortDescription>
        <value>Sorry, you do not have permission to register users.</value>
    </labels>
    <labels>
        <fullName>cmMemberRegistrationPlaceHolder</fullName>
        <categories>cm_MemberRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Placeholder for registration types</shortDescription>
        <value>Select Progress</value>
    </labels>
    <labels>
        <fullName>cmMemberRegistrationRegistrationTypeLabel</fullName>
        <categories>cm_MemberRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Registration Type combobox label</shortDescription>
        <value>Registration Type</value>
    </labels>
    <labels>
        <fullName>cmMemberRegistrationSaveAndNextButtonLabel</fullName>
        <categories>cm_MemberRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Save and Next button label</shortDescription>
        <value>Save &amp; Next</value>
    </labels>
    <labels>
        <fullName>cmMemberRegistrationSaveButtonLabel</fullName>
        <categories>cm_MemberRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Save button label</shortDescription>
        <value>Save</value>
    </labels>
    <labels>
        <fullName>cmMembersRegistrationModalLabel</fullName>
        <categories>cm_MembersRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Members Registration modal header</shortDescription>
        <value>Members Registration</value>
    </labels>
    <labels>
        <fullName>cmParticipationTypeLabel</fullName>
        <categories>cm_MembersRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Participation Type pick list label</shortDescription>
        <value>Participant type</value>
    </labels>
    <labels>
        <fullName>cmRegistrationLabel</fullName>
        <categories>cm_MemberRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Registration button label</shortDescription>
        <value>Registration</value>
    </labels>
    <labels>
        <fullName>cmRegistrationMembersNextButtonLabel</fullName>
        <categories>cm_MembersRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Next button label</shortDescription>
        <value>Next</value>
    </labels>
    <labels>
        <fullName>cmSelectMemberLabel</fullName>
        <categories>cm_MembersRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Select Member pick list label</shortDescription>
        <value>Select member</value>
    </labels>
    <labels>
        <fullName>cmSelfRegisterPermissionError</fullName>
        <categories>cm_SelfRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Uses for error message</shortDescription>
        <value>You do not have a contact. Please contact your system administrator.</value>
    </labels>
    <labels>
        <fullName>cmSelfRegisterSchoolFieldLabel</fullName>
        <categories>cm_MemberRegistrationComponent</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>School field label</shortDescription>
        <value>School</value>
    </labels>
</CustomLabels>

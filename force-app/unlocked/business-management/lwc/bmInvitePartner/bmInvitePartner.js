import { LightningElement, api, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { showErrors } from 'c/errorHandler';
import getForm from '@salesforce/apex/bm_InvitePartnersCtrl.getForm';
import saveRecord from '@salesforce/apex/bm_InvitePartnersCtrl.saveRecord';

export default class extends NavigationMixin(LightningElement) {
    @wire(CurrentPageReference) pageRef;
    @api postSavePageName;
    isLoading = false;

    connectedCallback() {
        this.getFormMethod = () => {
            return new Promise((resolve, reject) => {
                getForm()
                    .then(form => {
                        this.injectNameFields(form);
                        resolve(form);
                    })
                    .catch(error => reject(error));
            });
        };
    }

    injectNameFields(form) {
        ['Last Name', 'First Name'].forEach(field => {
            form.sections[0]?.columns[0]?.fields.unshift({
                label: field,
                name: field.replace(' ', ''),
                type: 'string',
                isUiRequired: true
            });
        });
    }

    handleSubmit(event) {
        const record = event.detail.record;

        this.isLoading = true;
        saveRecord({ record })
            .then(() => {
                const ev = new ShowToastEvent({
                    title: 'Success!',
                    variant: 'success'
                });
                this.dispatchEvent(ev);
                this.redirectToPostSavePage();
            })
            .catch((error) => showErrors(this, error))
            .finally(() => (this.isLoading = false));
    }

    redirectToPostSavePage() {
        if (!this.postSavePageName) {
            return;
        }
        const currentPage = this.pageRef.attributes.name;

        if (currentPage === this.postSavePageName) {
            window.location.reload();
        } else {
            this[NavigationMixin.Navigate]({
                type: 'comm__namedPage',
                attributes: {
                    name: this.postSavePageName
                }
            });
        }
    }
}

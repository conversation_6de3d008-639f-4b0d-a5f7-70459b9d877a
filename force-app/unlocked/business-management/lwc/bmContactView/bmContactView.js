import { api, LightningElement } from 'lwc';
import { showErrors } from 'c/errorHandler';
import getForm from '@salesforce/apex/bm_ContactViewCtrl.getForm';
import saveRecord from '@salesforce/apex/bm_ContactViewCtrl.saveRecord';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

import successMessage from '@salesforce/label/c.bm_Success';
import successfullyUpdateMessage from '@salesforce/label/c.bm_SuccessfullyUpdateMessage';

export default class extends LightningElement {
    @api recordId;
    @api mode = 'readonly';
    getFormMethod = getForm;
    isLoading = false;
    overrides = [
        { name: 'Email', disabled: true },
        { name: 'Name', disabled: true }
    ];

    labels ={
        successMessage,
        successfullyUpdateMessage,
    }

    connectedCallback() {
        this.getFormMethod = () => getForm({ contactId: this.recordId }).catch((error) => showErrors(this, error));
    }

    save(ev) {
        let record = ev.detail.record;
        record.Id = this.recordId;
        this.isLoading = true;

        saveRecord({ record: record })
            .then(() => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: this.labels.successMessage,
                        message: this.labels.successfullyUpdateMessage,
                        variant: 'success'
                    })
                );
            })
            .catch((error) => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }
}

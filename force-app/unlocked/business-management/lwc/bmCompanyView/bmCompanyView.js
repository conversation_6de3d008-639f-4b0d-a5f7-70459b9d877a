import { LightningElement, api } from 'lwc';
import { showErrors } from 'c/errorHandler';
import getForm from '@salesforce/apex/bm_CompanyCtrl.getForm';
import saveRecord from '@salesforce/apex/bm_CompanyCtrl.saveRecord';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class extends LightningElement {
    @api mode = 'readonly';
    getFormMethod = getForm;
    isLoading = false;
    overrides = [{ name: 'BillingAddress', country: 'US', countryDisabled: true }];

    save(ev) {
        this.isLoading = true;
        saveRecord({ record: ev.detail.record })
            .then(() => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Success.',
                        message: 'The record is successfully updated.',
                        variant: 'success'
                    })
                );
            })
            .catch((error) => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }
}

## General Settings
Assign `BM Guest Permissions` permission set to Guest user to allow them create record.

## Permission Sets

| Name                               | Description |
|:-----------------------------------|-------------|
| BM Admin Permissions               |             |
| BM Partner Lead Permissions        |             |
| BM Partner Permissions             |             |
| BM Guest Permissions               | 	           |

## FLOW
Please add the exact time (Start Date and Start Time) of triggering flow  directly in the flow. (Partners notification flow)

## Supported settings:

| Name                      | Possible Values                         | Description                                                   |
|---------------------------|-----------------------------------------|---------------------------------------------------------------|
| DEL bm_OrgWideEmailId     | Id of org wide email object / no record | Id of org wide email used for sending emails to lead creators |
| bm_PortalPageLink         | Link                                    | Link to the community used in Partner Flow notification 	     |
| DEL bm_PartnerPermissions |                                         |                                                               |

## Community Settings
To display a list of contacts from a partner account on the “My Company” page, 
you need to place Record List component somewhere on the page and specify a Filter Name as All Company Contacts
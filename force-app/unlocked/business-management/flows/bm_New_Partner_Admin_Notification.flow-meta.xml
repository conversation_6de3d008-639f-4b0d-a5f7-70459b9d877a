<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>New_Partner</name>
        <label>New Partner</label>
        <locationX>176</locationX>
        <locationY>947</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>CustomNotification.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>UserIds</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <stringValue>New Partner</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>NotificationBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <assignments>
        <name>Add_id</name>
        <label>Add id</label>
        <locationX>264</locationX>
        <locationY>647</locationY>
        <assignmentItems>
            <assignToReference>UserIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Admin_user.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Admin_user</targetReference>
        </connector>
    </assignments>
    <description>Notify admins about new partner</description>
    <environments>Default</environments>
    <interviewLabel>[BM] New Partner Admin Notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[BM] New Partner Admin Notification</label>
    <loops>
        <name>Admin_user</name>
        <label>Admin user</label>
        <locationX>176</locationX>
        <locationY>539</locationY>
        <collectionReference>Get_Admin_Users</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>CustomNotification</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>CustomNotification</name>
        <label>Custom Notification</label>
        <locationX>176</locationX>
        <locationY>839</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>New_Partner</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>bm_NewPartnerNotification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Admin_Profile</name>
        <label>Get Admin Profile</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Admin_Users</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>System Administrator</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Profile</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Admin_Users</name>
        <label>Get Admin Users</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Admin_user</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ProfileId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Admin_Profile.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Admin_Profile</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>bm_IsPartnerInvited__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Contact</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Draft</status>
    <textTemplates>
        <name>NotificationBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>New Partner {!$Record.FirstName}  {!$Record.LastName} was created by {!$Record.CreatedBy.FirstName} {!$Record.CreatedBy.LastName}</text>
    </textTemplates>
    <variables>
        <name>UserIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>

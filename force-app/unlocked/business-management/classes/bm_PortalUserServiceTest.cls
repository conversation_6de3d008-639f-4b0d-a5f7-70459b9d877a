@IsTest
public class bm_PortalUserServiceTest {
    @TestSetup
    static void setup() {
        bm_TestUtils.insertSettings();
        createUsers();
    }

    @Future
    static void createUsers() {
        bm_TestUtils.createAdminUser();
        System.runAs(bm_TestUtils.getAdmin()) {
            Account theAccount = bm_TestUtils.createAccount('Acme');
            Contact theContact = bm_TestUtils.createContact(theAccount.Id, 'Partner');
        }
    }

    @IsTest
    static void createPortalUserSuccess() {
        Contact theContact = [SELECT FirstName, LastName, Email, PortalRole__c FROM Contact WHERE Account.Name = 'Acme' LIMIT 1];

        Test.startTest();
        System.runAs(bm_TestUtils.getAdmin()) {
            bm_PortalUserService.createNewUser(theContact);
        }
        Test.stopTest();

        User portalUser = [SELECT Id FROM User WHERE ContactId = :theContact.Id];
        Assert.areNotEqual(null, portalUser, 'User must be created');
    }

    @IsTest
    static void createPortalUserErrorNoInputData() {
        String errorMessage;

        Test.startTest();
        System.runAs(bm_TestUtils.getAdmin()) {
            try {
                bm_PortalUserService.createNewUser(null);
            } catch (Exception ex) {
                errorMessage = ex.getMessage();
            }
        }
        Test.stopTest();

        // Assert.areEqual(System.Label.bmPortalUserNotDataInput, errorMessage, 'Wrong error message');
    }
}
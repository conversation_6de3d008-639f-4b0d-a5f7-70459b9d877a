public with sharing class bm_InvitePartnersCtrl {
    private static final String FORM_LAYOUT_NAME = 'Contact-[BM] Partner Invitation';
    private static final String ROLE = 'Partner';

    @AuraEnabled
    public static aclab.FormLayout getForm() {
        try {
            return aclab.Form.retrieve(FORM_LAYOUT_NAME);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void saveRecord(Contact record) {
        try {
            Contact theContact = (Contact) aclab.Form.cleanInjectedFields(record, FORM_LAYOUT_NAME);
            theContact.AccountId = PortalUser.getAccountId();
            theContact.bm_IsPartnerInvited__c = true;
            theContact.PortalRole__c = ROLE;
            theContact.FirstName = record.FirstName;
            theContact.LastName = record.LastName;
            WS.insertRecord(theContact);
            bm_PortalUserService.createNewUser(theContact);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}
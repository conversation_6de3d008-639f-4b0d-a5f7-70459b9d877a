@IsTest
public class bm_PartnersRegistrationFormCtrlTest {
    @TestSetup
    static void setup() {
        bm_TestUtils.insertSettings();
        createUsers();
    }

    @Future
    static void createUsers() {
        bm_TestUtils.createAdminUser();
    }

    @IsTest
    static void geContactFormSectionsSuccess() {
        aclab.FormLayout theForm = null;
        System.runAs(bm_TestUtils.getAdmin()) {
            Test.startTest();
            theForm = bm_PartnersRegistrationFormCtrl.getForm();
            Test.stopTest();
        }
        Assert.areNotEqual(0, theForm.sections.size(), 'Sections size must not be 0');
    }

    @IsTest
    static void createLeadValidCaptcha() {
        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Boolean isError = false;
        List<Lead> leads;
        System.runAs(bm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    bm_PartnersRegistrationFormCtrl.saveRecord(
                        new Lead(LastName = 'Tony', Company = 'TTM'),
                        String.valueOf(bm_TestUtils.getPartnerLeadRecordTypeId()),
                        'captcha',
                        false
                    );
                } catch (Exception ex) {
                    isError = true;
                }
            }

            leads = [SELECT Id FROM Lead];
            Test.stopTest();
        }

        Assert.isFalse(isError, 'Captcha must be valid');
        Assert.areEqual(1, leads.size(), 'One Lead must be created');
    }

    @IsTest
    static void createLeadNotValidCaptcha() {
        Boolean isError = false;
        List<Lead> leads;
        System.runAs(bm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    bm_PartnersRegistrationFormCtrl.saveRecord(
                        new Lead(LastName = 'Tony', Company = 'TTM'),
                        String.valueOf(bm_TestUtils.getPartnerLeadRecordTypeId()),
                        null,
                        false
                    );
                } catch (Exception ex) {
                    isError = true;
                }
            }

            leads = [SELECT Id FROM Lead];
            Test.stopTest();
        }

        Assert.isTrue(isError, 'Captcha must not be valid');
        Assert.areEqual(0, leads.size(), 'Lead must not be created');
    }

    @IsTest
    static void captchaIsValidError() {
        Boolean isError = false;

        System.runAs(bm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    bm_PartnersRegistrationFormCtrl.saveRecord(
                        new Lead(LastName = 'Tony', Company = 'TTM'),
                        String.valueOf(bm_TestUtils.getPartnerLeadRecordTypeId()),
                        'captcha',
                        false
                    );
                } catch (Exception ex) {
                    isError = true;
                }
            }
            Test.stopTest();
        }

        Assert.isTrue(isError, 'Error should exist');
    }

    @IsTest
    static void createLeadNullError() {
        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Test.startTest();
        try {
            bm_PartnersRegistrationFormCtrl.saveRecord(
                null,
                String.valueOf(bm_TestUtils.getPartnerLeadRecordTypeId()),
                'captcha',
                false
            );
            Assert.fail('createLead() expected to fail with null record passed');
        } catch (Exception e) {
            Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
        }
        Test.stopTest();
    }

    @IsTest
    static void saveRecordEmailError() {
        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Boolean isError = false;
        List<Lead> leads;
        System.runAs(bm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    bm_PartnersRegistrationFormCtrl.saveRecord(
                        new Lead(LastName = 'Tony', Company = 'TTM'),
                        String.valueOf(bm_TestUtils.getPartnerLeadRecordTypeId()),
                        'captcha',
                        true
                    );
                } catch (Exception ex) {
                    isError = true;
                }
            }

            leads = [SELECT Id FROM Lead];
            Test.stopTest();
        }

//        Assert.isTrue(isError, '');
    }
}
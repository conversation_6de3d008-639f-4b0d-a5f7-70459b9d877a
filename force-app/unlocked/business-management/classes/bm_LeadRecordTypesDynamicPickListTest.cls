@IsTest
public class bm_LeadRecordTypesDynamicPickListTest {
    @TestSetup
    static void setup() {
        bm_TestUtils.insertSettings();
        insertAdmin();
    }

    @Future
    static void insertAdmin() {
        bm_TestUtils.createAdminUser();
    }

    @IsTest
    static void getDefaultValueTest() {
        VisualEditor.DataRow defaultValue;
        Test.startTest();
        System.runAs(bm_TestUtils.getAdmin()) {
            defaultValue = new bm_LeadRecordTypesDynamicPickList().getDefaultValue();
        }
        Test.stopTest();

        System.assertEquals('--None--', defaultValue.getLabel(), 'Unexpected label for default value');
        System.assertEquals('', defaultValue.getValue(), 'Default value should be empty string');
    }

    @IsTest
    static void getValuesTest() {
        VisualEditor.DynamicPickListRows values;
        Test.startTest();
        System.runAs(bm_TestUtils.getAdmin()) {
            values = new bm_LeadRecordTypesDynamicPickList().getValues();
        }
        Test.stopTest();

        System.assertEquals(3, values.size(), 'Invalid total number of options');
    }
}
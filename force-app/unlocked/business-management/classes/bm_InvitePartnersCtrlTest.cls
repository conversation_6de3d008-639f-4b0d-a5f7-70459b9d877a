@IsTest
public class bm_InvitePartnersCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(bm_TestUtils.createAdminUser()) {
            bm_TestUtils.insertSettings();
            Account theAccount = bm_TestUtils.createAccount('Acme');
            Contact theContact = bm_TestUtils.createContact(theAccount.Id, 'Partner Lead');
            PortalUser.create(theContact);
        }
    }

    @Future
    static void createUsers() {
        bm_TestUtils.createAdminUser();
        System.runAs(bm_TestUtils.getAdmin()) {
            Account theAccount = bm_TestUtils.createAccount('Acme');
            Contact theContact = bm_TestUtils.createContact(theAccount.Id, 'Partner Lead');
            PortalUser.create(theContact);
        }
    }

    @IsTest
    static void geContactFormSectionsSuccess() {
        aclab.FormLayout theForm = null;
        System.runAs(bm_TestUtils.getUserByRole('Partner Lead')) {
            Test.startTest();
            theForm = bm_InvitePartnersCtrl.getForm();
            Test.stopTest();
        }
        Assert.areNotEqual(0, theForm.sections.size(), 'Sections size must not be 0');
    }

    @IsTest
    static void createPartnerSuccess() {
        Contact contactRecord = new Contact(FirstName = 'Tom', LastName = 'Smith', Email = '<EMAIL>');

        Test.startTest();
        System.runAs(bm_TestUtils.getUserByRole('Partner Lead')) {
            bm_InvitePartnersCtrl.saveRecord(contactRecord);
        }
        Test.stopTest();

        User newPartner = [SELECT Id FROM User WHERE Email = :contactRecord.Email];

        Assert.areNotEqual(null, newPartner.Id, 'Partner must be created');
    }

    @IsTest
    static void createPartnerError() {
        Contact contactRecord = new Contact(FirstName = 'Tom', LastName = 'Smith');
        Test.startTest();
        System.runAs(bm_TestUtils.getUserByRole('Partner Lead')) {
            try {
                bm_InvitePartnersCtrl.saveRecord(contactRecord);
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }
}
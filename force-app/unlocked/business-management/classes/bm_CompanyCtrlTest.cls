@IsTest
private class bm_CompanyCtrlTest {
    @TestSetup
    static void setup() {
        bm_TestUtils.insertSettings();
    }

    @IsTest
    static void getFormTest() {
        Boolean isExceptionThrown = false;
        Account theAccount = bm_TestUtils.createAccount('Test');
        bm_CompanyCtrl.testAccountId = theAccount.Id;
        Test.startTest();
        try {
            bm_CompanyCtrl.getForm();
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isFalse(isExceptionThrown, 'form should be retrieved successfully');
    }

    @IsTest
    static void getFormExceptionTest() {
        Boolean isExceptionThrown = false;
        Test.startTest();
        try {
            bm_CompanyCtrl.getForm();
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isTrue(isExceptionThrown, 'exception should be thrown');
    }

    @IsTest
    static void saveRecordTest() {
        Boolean isExceptionThrown = false;
        Account theAccount = bm_TestUtils.createAccount('Test');
        theAccount.Name = 'Test2';
        bm_CompanyCtrl.testAccountId = theAccount.Id;
        Test.startTest();
        try {
            bm_CompanyCtrl.saveRecord(theAccount);
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isFalse(isExceptionThrown, 'account should saved successfully');
    }

    @IsTest
    static void saveRecordWithoutIdTest() {
        Boolean isExceptionThrown = false;
        Account theAccount = bm_TestUtils.createAccount('Test');
        theAccount.Name = 'Test2';
        Test.startTest();
        try {
            bm_CompanyCtrl.saveRecord(theAccount);
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isTrue(isExceptionThrown, 'exception should be thrown');
    }
}
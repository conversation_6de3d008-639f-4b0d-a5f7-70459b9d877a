public without sharing class bm_PortalUserService {
    public static void createNewUser(Contact theContact) {
        checkUserByEmail(theContact.Email);
        PortalUser.create(theContact);
    }

    private static void checkUserByEmail(String email) {
        Integer urCount = [SELECT COUNT() FROM User WHERE Username = :email AND IsActive = TRUE WITH USER_MODE];
        if (urCount != 0) {
            throw Error.exception(System.Label.bm_UserExistMessage);
        }
    }
}
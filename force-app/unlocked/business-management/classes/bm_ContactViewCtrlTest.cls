@IsTest
public with sharing class bm_ContactViewCtrlTest {
    @TestSetup
    static void setup() {
        bm_TestUtils.insertSettings();
    }

    @IsTest
    static void getFormTest() {
        Boolean isExceptionThrown = false;
        Account theAccount = bm_TestUtils.createAccount('Test');
        Contact theContact = bm_TestUtils.createContact(theAccount.Id, 'Partner Lead');

        Test.startTest();
        try {
            bm_ContactViewCtrl.getForm(theContact.Id);
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isFalse(isExceptionThrown, 'form should be retrieved successfully');
    }

    @IsTest
    static void getFormWithoutIdTest() {
        Boolean isExceptionThrown = false;

        Test.startTest();
        try {
            bm_ContactViewCtrl.getForm(null);
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isTrue(isExceptionThrown, 'exception should be thrown');
    }

    @IsTest
    static void saveRecordTest() {
        Boolean isExceptionThrown = false;
        Account theAccount = bm_TestUtils.createAccount('Test');
        Contact theContact = bm_TestUtils.createContact(theAccount.Id, 'Partner Lead');
        theContact.Phone = '123';
        Test.startTest();
        try {
            bm_ContactViewCtrl.saveRecord(theContact);
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isFalse(isExceptionThrown, 'contact should saved successfully');
    }

    @IsTest
    static void saveRecordWithoutIdTest() {
        Boolean isExceptionThrown = false;
        Test.startTest();
        try {
            bm_ContactViewCtrl.saveRecord(new Contact());
        } catch (Exception e) {
            isExceptionThrown = true;
        }
        Test.stopTest();
        Assert.isTrue(isExceptionThrown, 'exception should be thrown');
    }
}
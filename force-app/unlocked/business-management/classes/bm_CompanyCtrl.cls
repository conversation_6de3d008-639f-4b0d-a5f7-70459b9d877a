public with sharing class bm_CompanyCtrl {
    @TestVisible
    private static Id testAccountId;

    private static final String FORM_LAYOUT_NAME = 'Account-[BM] Company - Portal View';

    @AuraEnabled
    public static aclab.FormLayout getForm() {
        try {
            Id accountId = Test.isRunningTest() ? testAccountId : PortalUser.getAccountId();
            return aclab.Form.retrieve(FORM_LAYOUT_NAME, accountId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Id saveRecord(Account record) {
        try {
            // Throw error if current user not Lead Advisor
            if (!PortalUser.hasRole(PortalUser.Role.PARTNER_LEAD) && !Test.isRunningTest()) {
                throw Error.toLWC('Permission denied.');
            }

            Account school = (Account) aclab.Form.cleanInjectedFields(record, FORM_LAYOUT_NAME);
            school.Id = Test.isRunningTest() ? testAccountId : PortalUser.getAccountId();
            WS.updateRecord(school);
            return school.Id;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}
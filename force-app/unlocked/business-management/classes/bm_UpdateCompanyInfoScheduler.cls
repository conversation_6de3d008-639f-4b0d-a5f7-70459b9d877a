global with sharing class bm_UpdateCompanyInfoScheduler implements Schedulable {
    global void execute(SchedulableContext ctx) {
        if (this.isDateToSendNotification()) {
            {
                // Do
                // UpdateCompanyInfoEmailTemplate__c

                List<Contact> partners = [
                    SELECT Id, Email, AccountId
                    FROM Contact
                    WHERE PortalRole__c INCLUDES ('Partner Lead')
                ];

                String emailTemplateName = bm_Setting__c.getInstance().UpdateCompanyInfoEmailTemplate__c;
                List<EmailTemplate> templates = [
                    SELECT Id
                    FROM EmailTemplate
                    WHERE DeveloperName = :emailTemplateName AND IsActive = TRUE
                ];

                if (partners.isEmpty() || templates.isEmpty()) {
                    return;
                }

                this.sendEmail(partners, templates[0]);
            }
        }
    }

    private Boolean isDateToSendNotification() {
        Date today = Date.today();

        String expirationDate = bm_Setting__c.getInstance().UpdateCompanyInfoNotificationDate__c;

        // Split the string '31/08' into month and day parts
        List<String> dateParts = expirationDate.split('/');
        Integer month = Integer.valueOf(dateParts[1]);
        Integer day = Integer.valueOf(dateParts[0]);

        // Create a date instance using the current year, month, and day
        Date targetDate = Date.newInstance(today.year(), month, day);

        return today == targetDate;
    }

    @TestVisible
    private void sendEmail(List<Contact> partners, EmailTemplate template) {
        String orgWideId = (String) CoreSetting__c.getInstance().OrgWideEmailId__c;
        List<Messaging.SingleEmailMessage> emailMessages = new List<Messaging.SingleEmailMessage>();

        for (Contact partner : partners) {
            if (partner.Email != null) {
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                email.setTargetObjectId(partner.Id);
                email.setTemplateId(template.Id);
                email.setOrgWideEmailAddressId(orgWideId);
                emailMessages.add(email);
            }
        }

        Messaging.sendEmail(emailMessages, false);
    }
}
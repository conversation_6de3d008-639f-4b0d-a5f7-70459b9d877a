@IsTest
public class bm_UpdateCompanyInfoSchedulerTest {
    @TestSetup
    static void setup() {
        bm_TestUtils.insertSettings();
    }

    static void createUsers() {
        bm_TestUtils.createAdminUser();
        System.runAs(bm_TestUtils.getAdmin()) {
            Account theAccount = bm_TestUtils.createAccount('Acme');
            Contact theContact = bm_TestUtils.createContact(theAccount.Id, 'Partner');
        }
    }

    @IsTest
    static void geContactFormSectionsSuccess() {
        createUsers();
        System.runAs(bm_TestUtils.getAdmin()) {
            bm_UpdateCompanyInfoScheduler myClass = new bm_UpdateCompanyInfoScheduler();
            String chron = '0 0 20 * * ?';
            System.schedule('Test', chron, myClass);
        }
    }

    @IsTest
    static void testSendEmail() {
        createUsers();
        System.runAs(bm_TestUtils.getAdmin()) {
            // Create Email Template
            EmailTemplate testTemplate = new EmailTemplate(
                    Name = 'Test Template',
                    DeveloperName = 'Test_Template',
                    TemplateType = 'text',
                    Subject = 'Test Subject',
                    HtmlValue = 'Test Body',
                    IsActive = true,
                    FolderId = UserInfo.getUserId()
            );
            insert testTemplate;


            // Create Contacts
            List<Contact> contacts = new List<Contact>{
                    new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>'),
                    new Contact(FirstName = 'Jane', LastName = 'Smith', Email = null),
                    new Contact(FirstName = 'Alice', LastName = 'Johnson', Email = '<EMAIL>')
            };
            insert contacts;

            Test.startTest();
            new bm_UpdateCompanyInfoScheduler().sendEmail(contacts, testTemplate);
            Test.stopTest();
        }

    }
}
public with sharing class bm_ContactViewCtrl {
    private static final String FORM_LAYOUT_NAME = 'Contact-[BM] Contact - Portal View';

    @AuraEnabled
    public static aclab.FormLayout getForm(Id contactId) {
        try {
            return aclab.Form.retrieve(FORM_LAYOUT_NAME, contactId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Id saveRecord(Contact record) {
        try {
            // Throw error if current user not Partner Lead
            if (!PortalUser.hasRole(PortalUser.Role.PARTNER_LEAD) && !Test.isRunningTest()) {
                throw Error.toLWC('Permission denied.');
            }

            Contact theContact = (Contact) aclab.Form.cleanInjectedFields(
                record,
                FORM_LAYOUT_NAME,
                new Set<String>{ 'Name', 'Email' }
            );
            WS.updateRecord(theContact);
            return theContact.Id;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}